import React, { ReactNode } from "react";
import { render, screen } from "@testing-library/react";
import Footer from "./Footer";
import { axe } from "jest-axe";
import { FormProvider, useForm, useFormContext } from "react-hook-form";

jest.mock("react-hook-form", () => ({
  ...jest.requireActual("react-hook-form"),
  useFormContext: jest.fn()
}));

describe("Footer", () => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    const formMethods = useForm();
    return <FormProvider {...formMethods}>{children}</FormProvider>;
  };

  const footerInput = {
    buttons: { cancel: "Cancel", next: "Next" },
    onCancel: jest.fn(),
    disableSubmit: true,
    isPending: true,
    isFranchisesYouPlayPage: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useFormContext as jest.Mock).mockReturnValue({
      formState: {
        errors: {},
        isValid: true
      }
    });
  });

  it("includes two buttons: Cancel and Next", async () => {
    render(<Footer {...footerInput} />);

    expect(await screen.findByText(/Cancel/i)).toBeInTheDocument();
    expect(await screen.findByText(/Next/i)).toBeInTheDocument();
  });

  it("changes 'next' button label to Submit", async () => {
    render(<Footer {...{ ...footerInput, buttons: { cancel: "Cancel", next: "Submit" } }} />);

    expect(await screen.findByText(/Cancel/i)).toBeInTheDocument();
    expect(await screen.findByText(/Submit/)).toBeInTheDocument();
  });

  it("disables next button if the form has validation errors", () => {
    (useFormContext as jest.Mock).mockReturnValue({
      formState: {
        errors: { fieldName: { message: "Error message" } },
        isValid: false
      }
    });

    render(<Footer {...{ ...footerInput }} />);

    const nextButton = screen.getByText(/Next/i);
    expect(nextButton).toBeDisabled();
  });

  it("enables button when no errors and form is valid", () => {
    (useFormContext as jest.Mock).mockReturnValue({
      formState: {
        errors: {},
        isValid: true
      }
    });

    render(<Footer {...{ ...footerInput, disableSubmit: false, isPending: false }} />);

    const nextButton = screen.getByText(/Next/i);
    expect(nextButton).not.toBeDisabled();
  });

  it("is accessible", async () => {
    (useFormContext as jest.Mock).mockReturnValue({
      formState: {
        errors: {},
        isValid: true
      }
    });

    const { container } = render(
      <Wrapper>
        <Footer {...footerInput} />
      </Wrapper>
    );

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
