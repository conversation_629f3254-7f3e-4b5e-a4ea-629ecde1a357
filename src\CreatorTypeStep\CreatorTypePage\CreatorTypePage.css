.creator-types-container-creator-type-container {
  @apply flex flex-col items-center;
}

.creator-types-container-creator-type-container form .card-container .image-as-icon {
  @apply mb-meas0 h-[96px] w-[100px] md:h-[94px] md:w-[130px] xl:h-meas38 xl:w-meas40;
}

.creator-types-container-creator-type-container form .card-container {
  @apply xs:w-auto md:lg:w-[90%] place-items-center md:w-[640px] md:m-auto xl:w-[100%];
}
.creator-types-container-creator-type-container form .creator-type-card-container {
  @apply max-w-[1440px] mb-[70px];
}

.creator-types-container-creator-type-description {
  @apply px-meas10 text-center font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large md:px-meas0;
}

.creator-types-container-creator-type-title {
  @apply pb-meas10 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}

.creator-types-container-creator-type-container .card-title {
  @apply xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small xl:text-desktop-body-default;
}

.creator-types-container-creator-type-container .form-error-message {
  @apply pb-meas35 text-error-50;
}
