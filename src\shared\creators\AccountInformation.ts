export type AccountInformationResponse = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName: string;
  lastName: string;
  originEmail: string;
  dateOfBirth: number;
  needsMigration: boolean;
  status: string;
  registrationDate: number;
  isPayable: boolean;
  creatorCode: string;
  preferredName: string;
  preferredPronouns: string;
};

export default class AccountInformation {
  readonly dateOfBirthAsText: number;

  static fromApi(data: AccountInformationResponse): AccountInformation {
    return new AccountInformation(
      data.defaultGamerTag,
      data.nucleusId,
      data.firstName,
      data.lastName,
      data.originEmail,
      data.dateOfBirth,
      data.needsMigration,
      data.status,
      data.registrationDate,
      data.isPayable,
      data.creatorCode,
      data.preferredName,
      data.preferredPronouns
    );
  }

  constructor(
    readonly defaultGamerTag: string,
    readonly nucleusId: number,
    readonly firstName: string,
    readonly lastName: string,
    readonly originEmail: string,
    readonly dateOfBirth: number,
    readonly needsMigration: boolean,
    readonly status: string,
    readonly registrationDate: number,
    readonly isPayable: boolean,
    readonly creatorCode: string,
    readonly preferredName: string,
    readonly preferredPronouns: string
  ) {
    this.dateOfBirthAsText = dateOfBirth;
  }
}
