import { type TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import TermsAndConditionsService from "./TermsAndConditionsService";
import { getPactSafeUrl } from "../../src/Factories/pactSafe";

jest.mock("@eait-playerexp-cn/http-client");

describe("TermsAndConditionsService", () => {
  it("clears signed status for the tier", async () => {
    const client = {
      delete: jest.fn()
    } as unknown as TraceableHttpClient;
    const service = new TermsAndConditionsService(client);
    const program = "sims_creator";

    await service.clearSignedStatusForProgram(program);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(
      `/api/terms-and-conditions/terms-and-conditions-status?program=${program}`
    );
  });

  it("fetches a signing url", async () => {
    const signerInformation = {
      businessName: "test",
      creatorId: "234324",
      country: "UK",
      email: "",
      firstName: "",
      lastName: "",
      screenName: "",
      locale: "en-us",
      program: "AFFILIATE"
    };
    const signingUrlResponse = getPactSafeUrl();
    const client = {
      post: jest.fn().mockResolvedValue({
        data: signingUrlResponse
      })
    } as unknown as TraceableHttpClient;
    const service = new TermsAndConditionsService(client);

    await service.getSigningUrl(signerInformation);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/api/terms-and-conditions/signing-url", { body: signerInformation });
  });
});
