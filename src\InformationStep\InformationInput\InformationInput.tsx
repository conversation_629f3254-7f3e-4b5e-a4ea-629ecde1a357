import { Controller, useFormContext } from "react-hook-form";
import React, { memo, RefObject, useEffect, useState } from "react";
import { DateInput, Input, Option, Select } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { isAdult, isObj, isString } from "../../utils";
import { Dispatch, FutureCreator, PlatformOption } from "../../types";
import CreatorWithProgramCode from "../../shared/creators/CreatorWithProgramCode";
import { CreatorWithProgamCodeProfile } from "../../Browser/CreatorService";
import { InformationStepConfiguration, Labels, PreferredPronounOtherOption } from "../OnboardingInformationStep";
import validationConfig from "../../utils/ValidationConfig";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Country } from "@eait-playerexp-cn/metadata-types";
import PrimaryPlatform from "../primaryPlatform/PrimaryPlatform";
import UpdateProfilePicture, { UpdateProfilePictureLabels } from "../upload/UpdateProfilePicture";
import AdditionalContentAndWebsiteLinks, {
  AdditionalContentAndWebsiteLinksRef
} from "../additionalContentAndWebsiteLinks/AdditionalContentAndWebsiteLinks";
import SubmittedContentService from "../../Browser/SubmittedContentService";

type InformationInputsProps = {
  labels: Labels;
  creator: CreatorWithProgamCodeProfile & CreatorWithProgramCode;
  countries: Option[];
  user: Record<string, unknown>;
  preferredPronouns: { value: string; label: string }[];
  stableDispatch: Dispatch;
  configuration: InformationStepConfiguration;
  platforms: PlatformOption[];
  futureCreator: FutureCreator;
  submittedContentService?: SubmittedContentService;
  additionalLinksRef: RefObject<AdditionalContentAndWebsiteLinksRef>;
};

const InformationInputs = memo(function InformationInputs({
  labels,
  creator,
  countries,
  preferredPronouns,
  configuration,
  platforms,
  stableDispatch,
  user,
  futureCreator,
  submittedContentService,
  additionalLinksRef
}: InformationInputsProps) {
  const methods = useFormContext();
  const { control, setValue, setError } = methods;
  const router = useRouter();
  const [preferredPronoun, setPreferredPronoun] = useState<string>();
  const { infoLabels, formLabels, layout, pageLabels } = labels;
  const { formFields } = configuration;

  const profilePictureLabels: UpdateProfilePictureLabels = {
    buttons: {
      browse: formLabels.browse,
      save: pageLabels.save,
      cancel: layout.buttons?.cancel,
      close: layout.buttons?.close
    },
    profilePicture: {
      title: formLabels.profilePicture?.title,
      avatarRequired: formLabels.profilePicture?.avatarRequired,
      avatarInvalid: formLabels.profilePicture?.avatarInvalid,
      avatarMoreThanLimit: formLabels.profilePicture?.avatarMoreThanLimit,
      message: formLabels.profilePicture?.message,
      termsAndConditionsFirst: formLabels.profilePicture?.termsAndConditionsFirst,
      termsAndConditionsMiddle: formLabels.profilePicture?.termsAndConditionsMiddle,
      termsAndConditionsLast: formLabels.profilePicture?.termsAndConditionsLast
    },
    profileLabels: {
      updateAvatar: formLabels.profilePicture?.title
    }
  };
  const additionalContentLabels = {
    labels: {
      additionalContentAndWebsiteTitle: formLabels.additionalContentAndWebsiteTitle,
      additionalContentAndWebsiteLinks: formLabels.additionalContentAndWebsiteDescription,
      additionalLinkPlaceholder: formLabels.additionalLinkPlaceholder,
      websiteUrlLabel: formLabels.websiteUrlLabel
    },
    addMoreUrlLabel: formLabels.addMoreUrlLabel,
    remove: formLabels.remove,
    messages: {
      duplicateUrl: formLabels.duplicateUrl
    },
    infoLabels,
    translation: labels.translation,
    formLabels: labels.formLabels,
    pageLabels: labels.pageLabels,
    layout: labels.layout
  };

  const rules = {
    firstName: {
      required: formFields.firstName.required ? infoLabels.messages.firstName : false,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: infoLabels.messages.firstNameTooLong }
    },
    lastName: {
      required: formFields.lastName.required ? infoLabels.messages.lastName : false,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: infoLabels.messages.lastNameTooLong }
    },
    preferredName: {
      required: formFields?.preferredName?.required || false,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: infoLabels.messages.preferredNameTooLong || "" }
    },
    preferredPronouns: {
      required: formFields?.preferredPronouns?.required || false
    },
    preferredPronoun: {
      required: formFields?.preferredPronoun?.required ? infoLabels.messages.preferredPronoun : false
    },
    dateOfBirth: {
      required: formFields.dateOfBirth.required ? infoLabels.messages.dateOfBirth : false,
      maxLength: { value: validationConfig.DATE_MAXLENGTH, message: infoLabels.messages.dateOfBirthInvalid },
      validate: (value: Date) => {
        if (!LocalizedDate.isValid(value)) {
          return infoLabels.messages.dateOfBirthInvalid;
        }
        if (isAdult(value as unknown as string)) {
          return infoLabels.messages.ageMustBe18OrOlder;
        }
        return true;
      }
    },
    country: {
      required: formFields?.country?.required ? infoLabels.messages.country : false,
      validate: (countryVal: Country | string): boolean | string => {
        const { label, name, value } = countryVal as Country;
        if (!label && !name && !value && isObj(countryVal as Country)) {
          // param is object for drop-down
          return infoLabels.messages.country;
        } else if (isString(countryVal.toString) && !countryVal) {
          return infoLabels.messages.country;
        }
        return true;
      }
    },
    street: {
      required: formFields?.street?.required ? infoLabels.messages.street : false,
      maxLength: { value: validationConfig.MAXLENGTH_255, message: infoLabels.messages.streetTooLong }
    },
    city: {
      required: formFields?.city?.required ? infoLabels.messages.city : false,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: infoLabels.messages.cityTooLong }
    },
    state: {
      required: formFields?.state?.required ? infoLabels.messages.state : false,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: infoLabels.messages.stateTooLong }
    },
    zipCode: {
      required: formFields?.zipCode?.required ? infoLabels.messages.zipCode : false,
      maxLength: { value: validationConfig.MAXLENGTH_20, message: infoLabels.messages.zipCodeTooLong }
    }
  };

  useEffect(() => {
    setValue("country", creator?.mailingAddress?.country || countries[0]);
  }, [countries, creator]);

  return (
    <>
      <div className="onboarding-mg-form-container">
        <div className="onboarding-information">
          <div className="onboarding-information-title">{formLabels.infoTitle}</div>
          <div className="onboarding-information-avatar">
            {!futureCreator && formFields.profilePicture && (
              <UpdateProfilePicture
                src={user.avatar as string}
                user={user as { avatar: string }}
                labels={profilePictureLabels}
                stableDispatch={stableDispatch}
                configuration={configuration}
              />
            )}
          </div>
        </div>
        <div className="onboarding-information-form">
          {formFields.firstName && (
            <Controller
              control={control}
              name="firstName"
              rules={rules.firstName}
              defaultValue={creator.accountInformation.firstName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  label={formLabels.labels.firstName}
                  placeholder={formLabels.labels.firstName}
                  id="firstName"
                />
              )}
            />
          )}
          {formFields.lastName && (
            <Controller
              control={control}
              name="lastName"
              rules={rules.lastName}
              defaultValue={creator.accountInformation.lastName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  label={formLabels.labels.lastName}
                  placeholder={formLabels.labels.lastName}
                  id="lastName"
                />
              )}
            />
          )}
          {formFields.preferredName && (
            <Controller
              control={control}
              name="preferredName"
              rules={rules.preferredName}
              defaultValue={creator.accountInformation.preferredName ?? creator.accountInformation.firstName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  label={formLabels.labels.preferredName}
                  placeholder={formLabels.labels.preferredName}
                  id="preferred-name"
                />
              )}
            />
          )}
          {formFields.preferredPronouns && (
            <Controller
              control={control}
              name="preferredPronouns"
              defaultValue={creator.preferredPronouns}
              render={({ field, fieldState: { error } }) => (
                <Select
                  id="preferred-pronouns"
                  errorMessage={error && error.message}
                  selectedOption={creator.preferredPronouns}
                  onChange={(item) => {
                    const pronounsItem = item as { value: string; label: string };
                    field.onChange(pronounsItem);
                    setPreferredPronoun(pronounsItem.value);
                  }}
                  options={preferredPronouns}
                  label={formLabels.labels.preferredPronouns}
                />
              )}
            />
          )}
          {formFields.preferredPronoun &&
            formFields.preferredPronouns &&
            preferredPronoun === PreferredPronounOtherOption && (
              <>
                <div></div>
                <div className="information-form-preferred-pronoun">
                  <Controller
                    control={control}
                    name="preferredPronoun"
                    defaultValue={creator.preferredPronoun}
                    rules={rules.preferredPronoun}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        label={formLabels.labels.preferredPronoun}
                        placeholder={formLabels.labels.enterPronoun}
                        id="preferred-prounoun"
                      />
                    )}
                  />
                </div>
              </>
            )}
          {formFields.defaultGamerTag && (
            <div>
              <div className="input-box-label">{formLabels.EAID}</div>
              <div className="onboarding-information-field">{creator.accountInformation.defaultGamerTag}</div>
            </div>
          )}
          {formFields.originEmail && (
            <div>
              <div className="input-box-label">{formLabels.eaEmailID}</div>
              <div className="onboarding-information-field">{creator.accountInformation.originEmail}</div>
            </div>
          )}
          {formFields.dateOfBirth && (
            <Controller
              control={control}
              name="dateOfBirth"
              rules={rules.dateOfBirth}
              defaultValue={creator.dateOfBirth()}
              render={({ field, fieldState: { error } }) => (
                <DateInput
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  label={formLabels.labels.dateOfBirth}
                  placeholder={formLabels.labels.dateOfBirth}
                  locale={router.locale}
                  maxDate={new Date()}
                  title={formLabels.calendar}
                  cancelText={formLabels.cancel}
                  okText={formLabels.ok}
                  onCancel={(date) => {
                    if (isAdult(date.toString())) {
                      setError(
                        "dateOfBirth",
                        { type: "manual", message: formLabels.ageMustBe18OrOlder },
                        { shouldFocus: true }
                      );
                    } else {
                      setError("dateOfBirth", {});
                    }
                    setValue("dateOfBirth", date);
                  }}
                />
              )}
            />
          )}
          {formFields.country && (
            <Controller
              control={control}
              name="country"
              rules={rules.country}
              defaultValue={creator?.mailingAddress?.country}
              render={({ field, fieldState: { error } }) => (
                <Select
                  id="country-information"
                  selectedOption={creator?.mailingAddress?.country}
                  errorMessage={error && error.message}
                  onChange={(item) => {
                    field.onChange(item);
                  }}
                  options={countries}
                  label={formLabels.labels.country}
                  dark
                />
              )}
            />
          )}
          {formFields.street && (
            <Controller
              control={control}
              name="street"
              rules={rules.street}
              defaultValue={creator?.mailingAddress?.street}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  id={formLabels.labels.street}
                  label={formLabels.labels.street}
                  placeholder={formLabels.labels.street}
                />
              )}
            />
          )}
          {formFields.city && (
            <Controller
              control={control}
              name="city"
              rules={rules.city}
              defaultValue={creator?.mailingAddress?.city}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  id={formLabels.labels.city}
                  label={formLabels.labels.city}
                  placeholder={formLabels.labels.city}
                />
              )}
            />
          )}

          {formFields.state && (
            <Controller
              control={control}
              name="state"
              rules={rules.state}
              defaultValue={creator?.mailingAddress?.state}
              render={({ field, fieldState: { error } }) => (
                <Input
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  id={formLabels.labels.state}
                  label={formLabels.labels.state}
                  placeholder={formLabels.labels.state}
                />
              )}
            />
          )}
          {formFields.zipCode && (
            <Controller
              control={control}
              name="zipCode"
              rules={rules.zipCode}
              defaultValue={creator?.mailingAddress?.zipCode}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id={formLabels.labels.zipCode}
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  label={formLabels.labels.zipCode}
                  placeholder={formLabels.labels.zipCode}
                />
              )}
            />
          )}
        </div>
      </div>
      {formFields.preferredPlatforms && platforms && (
        <PrimaryPlatform
          {...{
            primaryPlatform: creator.preferredPrimaryPlatforms,
            secondaryPlatforms: creator.preferredSecondaryPlatforms || [],
            options: platforms,
            labels
          }}
        />
      )}
      {formFields.socialLinks && (
        <AdditionalContentAndWebsiteLinks
          ref={additionalLinksRef}
          labels={additionalContentLabels}
          socialLinks={creator?.socialLinks ?? []}
          submittedContentService={submittedContentService}
        />
      )}
    </>
  );
});

export default InformationInputs;
