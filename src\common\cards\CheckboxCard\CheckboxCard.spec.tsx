import React from "react";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import CheckboxCard, { CheckboxCardProps, Item } from "./CheckboxCard";

describe("CheckboxCard", () => {
  const mockOnChange = jest.fn();
  const defaultItem: Item = {
    label: "Test Label",
    checked: false
  };

  const defaultProps: CheckboxCardProps = {
    onChange: mockOnChange,
    item: defaultItem,
    key: 1,
    basePath: "/support-a-creator"
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with default props", () => {
    render(<CheckboxCard {...defaultProps} />);

    expect(screen.getByLabelText("Test Label")).toBeInTheDocument();
  });

  it("should call onChange when checkbox is clicked", () => {
    render(<CheckboxCard {...defaultProps} />);

    fireEvent.click(screen.getByLabelText("Test Label"));

    waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(defaultItem, true);
    });
  });

  it("should not call onChange when disabled", () => {
    render(<CheckboxCard {...defaultProps} disabled={true} />);

    fireEvent.click(screen.getByLabelText("Test Label"));

    waitFor(() => {
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  it("should apply selected class when checked", () => {
    render(<CheckboxCard {...defaultProps} item={defaultItem} />);

    waitFor(() => {
      expect(screen.getByLabelText("Test Label").parentElement).toHaveClass("selected-card");
    });
  });

  it("should render icon if provided", () => {
    const MockIcon = () => <svg data-testid="mock-icon" />;
    render(<CheckboxCard {...defaultProps} item={{ ...defaultItem, icon: MockIcon }} />);

    expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
  });

  it("should render image as icon if provided", () => {
    render(<CheckboxCard {...defaultProps} item={{ ...defaultItem, imageAsIcon: "test-image.png" }} />);

    expect(screen.getByAltText("Icon image")).toBeInTheDocument();
  });

  it("should not call onChange when readOnly is true", () => {
    render(<CheckboxCard {...defaultProps} readOnly={true} />);

    fireEvent.click(screen.getByLabelText("Test Label") as HTMLInputElement);

    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it("should render label correctly", () => {
    render(<CheckboxCard {...defaultProps} />);

    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("should not render label if not provided", () => {
    render(<CheckboxCard {...defaultProps} item={{ ...defaultItem, label: undefined }} />);

    expect(screen.queryByText("Test Label")).not.toBeInTheDocument();
  });
});
