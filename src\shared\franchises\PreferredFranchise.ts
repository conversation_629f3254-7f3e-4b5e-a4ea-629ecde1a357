export type PreferredFranchiseResponse = { id: string; name: string; code: string; type: string };

export default class PreferredFranchise {
  static fromApi(data: PreferredFranchiseResponse): PreferredFranchise {
    return new PreferredFranchise(data.id, data.name, "", data.type);
  }

  constructor(
    readonly value: string,
    readonly label: string,
    readonly image: string,
    readonly type: string
  ) {
    this.image = image;
  }

  isPrimary(): boolean {
    return this.type === "PRIMARY";
  }
}
