import React from "react";
import { screen, waitFor } from "@testing-library/react";
import Information, { OnboardingInformationStepProps } from "./OnboardingInformationStep";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { aCountry, aLanguage, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { aLocalizedDate } from "../Factories/LocalizedDateBuilderFactories";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { useRouter } from "next/router";
import { renderPage } from "../Helpers/Page";
import userEvent from "@testing-library/user-event";
import CreatorService from "../Browser/CreatorService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationLabels } from "../Translations/Information";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../Browser/CreatorService");
jest.mock("next/router", () => ({
  useRouter: jest.fn(() => ({ locale: "en-us", push: jest.fn() }))
}));

describe("Information", () => {
  const labels = InformationLabels;
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY");
  const platforms = aPlatform({ value: "xbox", label: "xbox" });
  const countries = [aCountry({ label: "Canada", value: "Canada" }), aCountry()];
  const languages = [aLanguage({ value: "en", label: "English" })];
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const Configuration = {
    metadataClient: {} as TraceableHttpClient,
    creatorsClient: undefined,
    onBoardingClient: {} as TraceableHttpClient,
    DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
    formFields: {
      firstName: { required: true },
      lastName: { required: true },
      dateOfBirth: { required: true },
      country: { required: true },
      street: { required: true },
      city: { required: true },
      state: { required: true },
      zipCode: { required: true },
      tShirtSize: { required: true },
      entityType: { required: true },
      businessName: { required: true },
      email: { required: true },
      url: { required: true },
      followers: { required: true },
      defaultGamerTag: { required: true },
      originEmail: { required: true }
    },
    navigateToNextPage: "/creator-type"
  };
  const creatorService = {
    getCreatorWithProgramCode: jest.fn(),
    register: jest.fn()
  } as unknown as CreatorService;
  const metadataService = {
    getCountriesMatching: jest.fn().mockResolvedValue(countries),
    getLanguages: jest.fn().mockResolvedValue(languages),
    getLocales: jest.fn().mockResolvedValue(locales),
    getPlatformsMatching: jest.fn().mockResolvedValue([platforms])
  } as unknown as MetadataService;

  const informationProps: OnboardingInformationStepProps = {
    analytics: {},
    stableDispatch: jest.fn(),
    state: {},
    labels: labels,
    PROGRAM_CODE: "",
    router: useRouter(),
    errorHandling: () => {},
    configuration: Configuration,
    locale: "en",
    showConfirmation: false,
    setShowConfirmation: () => {},
    onClose: () => {},
    errorToast: () => {},
    FLAG_COUNTRIES_BY_TYPE: false,
    isExistingCreator: false,
    futureCreator: null as unknown as Record<string, unknown>,
    user: {},
    preferredPronounsOptions: [
      { value: "He / Him", label: "He / Him" },
      { value: "She / Her", label: "She / Her" },
      { value: "They / Them", label: "They / Them" },
      { value: "Other (Please specify)", label: "Other (Please specify)" },
      { value: "Prefer not to say", label: "Prefer not to say" }
    ],
    registrationCode: "123456"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (MetadataService as jest.Mock).mockReturnValue(metadataService);
    (CreatorService as jest.Mock).mockReturnValue(creatorService);
  });

  it("registers a creator", async () => {
    const payload = {
      accountInformation: {
        firstName: "Jane",
        lastName: "Doe",
        dateOfBirth: dateOfBirthAfter18years,
        country: countries,
        street: "Main St. 123",
        city: "Hyderabad",
        state: "Telangana",
        zipCode: "72000",
        primaryPlatform: platforms,
        secondaryPlatforms: [],
        nucleusId: "123456",
        originEmail: "<EMAIL>",
        defaultGamerTag: "test"
      },
      mailingAddress: {
        country: "Canada"
      },
      dateOfBirth: () => new LocalizedDate(dateOfBirthAfter18years as unknown as number).toDate()
    };
    const registerPayload = {
      information: {
        firstName: "JaneJane",
        lastName: "DoeDoe",
        nucleusId: payload.accountInformation.nucleusId,
        originEmail: payload.accountInformation.originEmail,
        country: payload.mailingAddress.country,
        street: payload.accountInformation.street,
        city: payload.accountInformation.city,
        state: payload.accountInformation.state,
        zipCode: payload.accountInformation.zipCode,
        creatorConnectedProgram: "",
        dateOfBirth: aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD")
      }
    };
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockReturnValue({
      data: { ...payload }
    });

    renderPage(<Information {...informationProps} />);

    await waitFor(() => {
      const firstName = screen.getByLabelText(/^First Name/i);
      expect(firstName.getAttribute("value")).toBe("");
    });
    await userEvent.type(screen.getByLabelText(/^First Name/i), payload.accountInformation.firstName);
    await userEvent.type(screen.getByLabelText(/^Last Name/i), payload.accountInformation.lastName);
    await userEvent.type(screen.getByLabelText(/^Street/i), payload.accountInformation.street);
    await userEvent.type(screen.getByLabelText(/^City/i), payload.accountInformation.city);
    await userEvent.type(screen.getByLabelText(/^State/i), payload.accountInformation.state);
    await userEvent.type(screen.getByLabelText(/^Zip Code/i), payload.accountInformation.zipCode);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(creatorService.register).toHaveBeenCalledTimes(1);
      expect(creatorService.register).toHaveBeenCalledWith(registerPayload);
    });
  });

  it("registers a creator with preferred pronouns", async () => {
    const payload = {
      accountInformation: {
        firstName: "Jane",
        lastName: "Doe",
        dateOfBirth: dateOfBirthAfter18years,
        country: countries,
        street: "Main St. 123",
        city: "Hyderabad",
        state: "Telangana",
        zipCode: "72000",
        primaryPlatform: platforms,
        secondaryPlatforms: [],
        nucleusId: "123456",
        originEmail: "<EMAIL>",
        defaultGamerTag: "test"
      },
      preferredPronouns: { value: "He / Him", label: "He / Him" },
      mailingAddress: {
        country: "Canada"
      },
      dateOfBirth: () => new LocalizedDate(dateOfBirthAfter18years as unknown as number).toDate()
    };
    const registerPayload = {
      information: {
        firstName: "JaneJane",
        lastName: "DoeDoe",
        nucleusId: payload.accountInformation.nucleusId,
        originEmail: payload.accountInformation.originEmail,
        country: payload.mailingAddress.country,
        street: payload.accountInformation.street,
        city: payload.accountInformation.city,
        state: payload.accountInformation.state,
        zipCode: payload.accountInformation.zipCode,
        creatorConnectedProgram: "",
        preferredName: "Janetemp",
        preferredPronouns: payload.preferredPronouns.value,
        dateOfBirth: aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD")
      }
    };
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockReturnValue({
      data: { ...payload }
    });

    renderPage(
      <Information
        {...informationProps}
        configuration={{
          ...Configuration,
          formFields: {
            ...Configuration.formFields,
            preferredName: { required: true },
            preferredPronoun: { required: true },
            preferredPronouns: { required: true }
          }
        }}
      />
    );

    await waitFor(() => {
      const firstName = screen.getByLabelText(/^First Name/i);
      expect(firstName.getAttribute("value")).toBe("Jane");
    });
    await userEvent.type(screen.getByLabelText(/^First Name/i), payload.accountInformation.firstName);
    await userEvent.type(screen.getByLabelText(/^Last Name/i), payload.accountInformation.lastName);
    await userEvent.type(screen.getByLabelText(/^Preferred Name/i), "temp");
    await userEvent.type(screen.getByLabelText(/^Street/i), payload.accountInformation.street);
    await userEvent.type(screen.getByLabelText(/^City/i), payload.accountInformation.city);
    await userEvent.type(screen.getByLabelText(/^State/i), payload.accountInformation.state);
    await userEvent.type(screen.getByLabelText(/^Zip Code/i), payload.accountInformation.zipCode);
    expect(await screen.findByText("Preferred Pronouns")).toBeInTheDocument();

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(creatorService.register).toHaveBeenCalledTimes(1);
      expect(creatorService.register).toHaveBeenCalledWith(registerPayload);
    });
  });
});
