.onboarding-information-page {
  @apply pb-meas16;
}
.onboarding-mg-form-container {
  @apply mt-meas20 flex w-full flex-col justify-between border-t border-white border-opacity-[0.33] pt-meas20 mb-meas16 mx-auto;
}
.onboarding-mg-form-container .input-box-label,
.onboarding-mg-form-container .select-label {
  @apply text-gray-10;
} /* Adding parent class for .select-label as we should not override core-ui-kit select label without parent class. It will impact globally*/
.onboarding-mg-form-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.onboarding-information-form {
  @apply grid grid-cols-1 gap-y-meas7 md:grid-cols-2 md:gap-x-meas20 md:gap-y-meas8;
}

.onboarding-information {
  @apply flex items-start justify-between mb-meas13 md:mb-meas25;
}

.onboarding-information-title {
  @apply font-display-bold font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.onboarding-information-subtitle {
  @apply pt-[10px] font-text-regular xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.onboarding-information-avatar {
  @apply mb-meas10 mt-[34px] flex justify-center self-center md:mb-meas0 md:mt-meas0 md:justify-end md:self-end;
}
.onboarding-mg-platform-container {
  @apply my-meas10 flex w-full flex-col items-center justify-center border-b border-t border-white border-opacity-[0.33] py-meas10 md:w-[640px];
}
.onboarding-mg-platform-title {
  @apply pb-meas7 font-display-regular font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.onboarding-mg-platform-description {
  @apply font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.onboarding-mg-platform-container .empty-card {
  @apply mt-meas5;
}
.onboarding-mg-platform-container .select-box {
  @apply mb-[12.97px] h-meas12 w-[290px] md:mb-[13.55px] md:w-[320px];
}
.onboarding-mg-platform-container .select-box .select-label {
  @apply text-white;
}
.onboarding-mg-platform-container .select-box .select-header {
  @apply h-auto;
}
.onboarding-mg-platform-container .select-box {
  @apply h-auto;
}
.onboarding-mg-platform-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.onboarding-mg-platform-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.onboarding-mg-sc-platform-container {
  @apply flex w-full flex-col items-center justify-center overflow-hidden xl:w-[1070px];
}
.onboarding-mg-sc-platform-container .mg-intro {
  @apply mb-[60px];
}
.onboarding-mg-secondary-title {
  @apply pb-meas7 font-display-regular font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.onboarding-mg-secondary-title[data-disabled="true"] {
  @apply opacity-50;
}
.onboarding-mg-form-container .input-box,
.select-header-label,
.from-text-field {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.onboarding-mg-form-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.onboarding-information-field {
  @apply flex min-h-[2.5rem] items-center font-text-regular text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.onboarding-mg-information-additional-content-container {
  @apply mb-meas10 mt-meas10 flex w-full flex-col justify-between border-t border-white border-opacity-[0.33] pt-meas10 md:w-[672px];
}
.onboarding-mg-information-additional-content {
  @apply flex w-full flex-col gap-meas7;
}
.onboarding-mg-information-additional-content .content-in-center {
  @apply flex w-[91.666667%] flex-col items-center md:w-[640px] xl:w-[790px];
}
.onboarding-mg-information-additional-content-title {
  @apply font-display-bold text-[1.5rem] leading-8 tracking-[1px] text-white xs:text-mobile-h4 md:text-center md:text-tablet-h4 lg:text-desktop-h4;
}
.onboarding-mg-information-additional-content-description {
  @apply text-center font-text-regular text-[1rem] font-normal leading-6;
}
.onboarding-intro-title {
  @apply pb-meas10 text-center font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 md:xs:mt-meas8;
}

.onboarding-intro-description {
  @apply font-text-regular text-center mx-auto max-w-[90%] text-desktop-body-default;
}
.onboarding-intro-description[data-disabled="true"] {
  @apply opacity-50;
}
.gamer-tag {
  @apply font-text-bold;
}
