import { Meta, StoryObj } from "@storybook/react";
import CancelRegistrationModal from "./CancelRegistrationModal";
import React, { ComponentType, ReactElement } from "react";

const meta: Meta<typeof CancelRegistrationModal> = {
  title: "Component Library/Cancel Registration Modal",
  component: CancelRegistrationModal,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CancelRegistrationModal>;

export const CancelRegistrationModalComponent: Story = {
  args: {
    labels: {
      title: "Cancel Registration",
      yes: "Yes",
      no: "No",
      close: "Close",
      confirmationDesc1: "Are you sure you want to cancel your registration?"
    },
    handleModalClose: () => {},
    handleCancelRegistration: () => {}
  }
};
