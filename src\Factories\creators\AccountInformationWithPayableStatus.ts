import { Factory } from "fishery";
import Random from "../Random";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { aLocalizedDate } from "../LocalizedDateBuilderFactories";
import AccountInformationWithPayableInfo from "../../shared/creators/AccountInformationWithPayableStatus";

const factory = Factory.define(() => ({
  avatar: Random.avatar(),
  defaultGamerTag: Random.userName(),
  nucleusId: Random.nucleusId(),
  firstName: Random.firstName(),
  lastName: Random.lastName(),
  originEmail: Random.email(),
  dateOfBirth: LocalizedDate.epochFromFormattedDate(
    aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY")
  ),
  needsMigration: Random.boolean(),
  status: Random.userStatus(),
  registrationDate: LocalizedDate.epochMinusDays(30),
  isPayable: Random.boolean()
}));

export function anAccountInformationWithPayableStatus(override = {}): AccountInformationWithPayableInfo {
  return factory.build(override) as AccountInformationWithPayableInfo;
}
