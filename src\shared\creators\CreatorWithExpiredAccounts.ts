import AccountInformation, { AccountInformationResponse } from "./AccountInformation";
import PreferredFranchise, { PreferredFranchiseResponse } from "../franchises/PreferredFranchise";
import MailingAddress from "./MailingAddress";
import PreferredPlatform, { PreferredPlatformResponse } from "../platforms/PreferredPlatform";
import CommunicationPreferences, { CommunicationPreferencesResponse } from "./CommunicationPreferences";
import AdditionalInformation, { AdditionalInformationResponse } from "./AdditionalInformation";
import LegalEntityInformation, { LegalEntityInformationResponse } from "./LegalEntityInformation";
import ConnectedAccount from "../channels/ConnectedAccount";
import crypto from "crypto";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import Identity from "../Identity";
import { MailingAddressPayload } from "@eait-playerexp-cn/creator-types";

export type CreatorWithExpiredAccountsResponse = {
  id: string;
  avatar: string;
  creatorTypes: string[];
  accountInformation: AccountInformationResponse;
  preferredPlatforms: PreferredPlatformResponse[];
  preferredFranchises: PreferredFranchiseResponse[];
  mailingAddress: MailingAddressPayload;
  connectedAccounts: ConnectedAccount[];
  additionalInformation: AdditionalInformationResponse;
  legalInformation: LegalEntityInformationResponse;
  communicationPreferences: CommunicationPreferencesResponse;
};

export type UpdatedIdentity = {
  id: string;
  accountInformation: AccountInformation & {
    defaultGamerTag: string;
    originEmail: string;
    lastLoginDate: string;
  };
};

export default class CreatorWithExpiredAccounts {
  public preferredPrimaryPlatforms: PreferredPlatform | null;
  public preferredSecondaryPlatforms: Array<PreferredPlatform>;
  public preferredPrimaryFranchises: PreferredFranchise | null;
  public preferredSecondaryFranchises: Array<PreferredFranchise>;
  readonly hasPointOfContact: boolean;
  readonly pointOfContactName?: string;
  readonly hasPOCDiscord: boolean;
  readonly pocDiscordTag?: string;
  readonly hashedId?: string;

  static fromApi(data: CreatorWithExpiredAccountsResponse): CreatorWithExpiredAccounts {
    return new CreatorWithExpiredAccounts(
      data.id,
      data.avatar,
      data.creatorTypes || [],
      AccountInformation.fromApi(data.accountInformation),
      data.preferredPlatforms.map((item) => PreferredPlatform.fromApi(item)),
      data.preferredFranchises.map((item) => PreferredFranchise.fromApi(item)),
      MailingAddress.fromApi(data.mailingAddress),
      CommunicationPreferences.fromApi(data.communicationPreferences),
      data.connectedAccounts || [],
      AdditionalInformation.fromApi(data.additionalInformation),
      data.legalInformation ? LegalEntityInformation.fromApi(data.legalInformation) : undefined
    );
  }

  constructor(
    readonly id: string,
    readonly avatar: string | null,
    readonly creatorTypes: Array<string>,
    public accountInformation: AccountInformation,
    preferredPlatforms: Array<PreferredPlatform>,
    preferredFranchises: Array<PreferredFranchise>,
    readonly mailingAddress: MailingAddress,
    readonly communicationPreferences: CommunicationPreferences,
    readonly connectedAccounts: Array<ConnectedAccount>,
    readonly additionalInformation: AdditionalInformation,
    readonly legalEntity?: LegalEntityInformation
  ) {
    this.avatar = avatar ?? "";
    this.preferredPrimaryPlatforms = preferredPlatforms.filter((item) => item.isPrimary() === true)[0] || null;
    this.preferredSecondaryPlatforms = preferredPlatforms.filter((item) => item.isPrimary() === false);
    this.preferredPrimaryFranchises = preferredFranchises.filter((item) => item.isPrimary() === true)[0] || null;
    this.preferredSecondaryFranchises = preferredFranchises.filter((item) => item.isPrimary() === false);
    this.hasPointOfContact = this.additionalInformation.pointOfContact !== null;
    this.hasPOCDiscord = this.hasPointOfContact && this.pocDiscordTag !== null;
  }

  isDisabled(): boolean {
    return this.accountInformation.status === "DISABLED";
  }

  isUnregistered(): boolean {
    return this.accountInformation.status === "UNREGISTERED";
  }

  isInactive(): boolean {
    return this.accountInformation.status === "INACTIVE";
  }

  originEmail(): string {
    return this.accountInformation?.originEmail;
  }

  nucleusId(): number {
    return this.accountInformation?.nucleusId;
  }

  username(): string {
    return this.accountInformation?.defaultGamerTag;
  }

  dateOfBirth(): number {
    return this.accountInformation?.dateOfBirth;
  }

  syncIdentity(identity: Identity): UpdatedIdentity {
    const updatedIdentity = {
      id: this.id,
      accountInformation: {
        ...this.accountInformation,
        defaultGamerTag: identity.defaultGamerTag,
        originEmail: identity.originEmail,
        lastLoginDate: LocalizedDate.formattedNow()
      }
    };
    this.accountInformation = updatedIdentity.accountInformation;
    return updatedIdentity;
  }

  analyticsId(): string {
    return this.hashedId
      ? this.hashedId
      : crypto.createHash("sha256").update(this.accountInformation.nucleusId.toString()).digest("base64");
  }

  connectedAccountsTypes(): string[] {
    return this?.connectedAccounts?.map((account) => account.type) || [];
  }

  contentLanguages(): string {
    return this?.communicationPreferences?.contentLanguages?.map((language) => language.label).join(",") || "";
  }

  creatorTypesLabels(): string[] {
    return this?.creatorTypes || [];
  }

  primaryFranchise(): string {
    return this?.preferredPrimaryFranchises?.label || "";
  }

  primaryPlatform(): string {
    return this?.preferredPrimaryPlatforms?.label || "";
  }

  secondaryFranchises(): string[] {
    return this?.preferredSecondaryFranchises?.map((franchise) => franchise.label) || [];
  }

  secondaryPlatforms(): string[] {
    return this?.preferredSecondaryPlatforms?.map((platform) => platform.label) || [];
  }

  pointOfContact(): string {
    return this.pointOfContactName || "";
  }
}
