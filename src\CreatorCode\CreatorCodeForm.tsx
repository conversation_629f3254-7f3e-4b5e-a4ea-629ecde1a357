import { Input, Toast } from "@eait-playerexp-cn/core-ui-kit";
import React, {
  memo,
  MouseEventHandler,
  ReactElement,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";
import { Controller, Message, useFormContext, ValidationRule } from "react-hook-form";

import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  ErrorResponse,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import { CloseHandler, Dispatch, ErrorHandling, OnboardingStepConfiguration, State, ValidationError } from "../types";
import { AxiosError } from "axios";
import ValidateCreatorCodeService from "../Browser/ValidateCreatorCodeService";
import { NextRouter } from "next/router";
import CancelRegistrationModal from "../common/CancelRegistrationModal/CancelRegistrationModal";
import { PageLabels } from "../InformationStep/OnboardingInformationStep";
import CreatorWithProgramCode from "../shared/creators/CreatorWithProgramCode";
import Footer from "../common/Footer/Footer";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { NavStep } from "../CreatorTypeStep/CreatorTypePage/CreatorTypePage";
import Form from "../utils/Form";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import CreatorService from "../Browser/CreatorService";

export type CreatorPageLabels = {
  title: string;
  description: string;
  tipsTitle: string;
  tip1: string;
  tip2: string;
  codeRequirements: string;
  customizeYourCode: string;
  confirmYourCode: string;
  next: string;
  cancel: string;
};

type MessageInfoLabels = {
  customizeCodeRequired: string;
  confirmCodeRequired: string;
  tooLong: string;
  specialCharactersNotAllowed: string;
  doNotMatch: string;
  alreadyInUse: string;
  codeIsInvalid: string;
  inappropriateLanguage: string;
};

export type CreatorCodeFormInputProps = {
  pageLabels: CreatorPageLabels & PageLabels;
  infoLabels: MessageInfoLabels;
  isPending: boolean;
  validationError: string;
  onClose: MouseEventHandler<HTMLButtonElement>;
  infoUnionPath: string;
};

type Pattern = {
  value: RegExp;
  message: string;
};

type MaxLength = {
  value: number;
  message: string;
};

type MaxLengthPatternRequired = {
  required: Message | ValidationRule<boolean>;
  maxLength: MaxLength;
  pattern: Pattern;
};

export type CreatorFormRules = {
  customizeCreatorCode: MaxLengthPatternRequired;
  confirmCreatorCode: MaxLengthPatternRequired;
};

const CreatorCodeFormInput = memo(function CreatorCodeFormInput({
  pageLabels,
  infoLabels,
  isPending,
  validationError,
  onClose,
  infoUnionPath
}: CreatorCodeFormInputProps) {
  const methods = useFormContext();
  const { control, formState, getValues, setError, clearErrors } = methods;
  const disableSubmit = Object.keys(formState.errors).length !== 0 || formState.isValid === false;
  const rules: CreatorFormRules = {
    customizeCreatorCode: {
      required: infoLabels.customizeCodeRequired,
      maxLength: {
        value: 20,
        message: infoLabels.tooLong
      },
      pattern: {
        value: /^[A-Za-z0-9]*$/,
        message: infoLabels.specialCharactersNotAllowed
      }
    },
    confirmCreatorCode: {
      required: infoLabels.confirmCodeRequired,
      maxLength: {
        value: 20,
        message: infoLabels.tooLong
      },
      pattern: {
        value: /^[A-Za-z0-9]*$/,
        message: infoLabels.specialCharactersNotAllowed
      }
    }
  };
  const compareTwoCodes = (value: string) => {
    const confirmYourCode = getValues("confirmYourCode");
    if (value && confirmYourCode) {
      clearErrors([`confirmYourCode`]);
      if (confirmYourCode !== value) {
        setError(`confirmYourCode`, { type: "manual", message: infoLabels.doNotMatch });
      }
    }
  };

  useEffect(() => {
    if (validationError) {
      setError(`customizeYourCode`, { type: "manual", message: validationError });
      setError(`confirmYourCode`, { type: "manual", message: validationError });
    }
  }, [setError, validationError]);

  const buttons = useMemo(
    () => ({
      cancel: pageLabels?.cancel ?? "",
      next: pageLabels?.next ?? ""
    }),
    [pageLabels]
  );

  return (
    <>
      <div className="creator-code-form-container">
        <div className="creator-code-form-information">
          <h3 className="creator-code-form-title">{pageLabels.title}</h3>
          <div className="creator-code-form-description">
            <div>{pageLabels.description}</div>
            <div className="creator-code-form-tips">
              <div>{pageLabels.tipsTitle}</div>
              <ul className="creator-code-list-container">
                <li className="creator-code-list">{pageLabels.tip1}</li>
                <li className="creator-code-list">{pageLabels.tip2}</li>
              </ul>
            </div>
          </div>
        </div>
        <div className="creator-code-form-validation-info-container">
          <img src={infoUnionPath} alt="" />
          <span className="creator-code-form-validation-info-label">{pageLabels.codeRequirements}</span>
        </div>
        <div className="creator-code-form-input-container">
          <Controller
            control={control}
            name="customizeYourCode"
            rules={{
              ...rules?.customizeCreatorCode,
              validate: (value) => {
                compareTwoCodes(value);
                return true;
              }
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="customizeYourCode"
                errorMessage={error?.message || ""}
                {...field}
                label={pageLabels.customizeYourCode}
                placeholder={pageLabels.customizeYourCode}
                dark
                validated={!disableSubmit}
              />
            )}
          />
        </div>
        <div className="creator-code-form-input-container">
          <Controller
            control={control}
            name="confirmYourCode"
            rules={{
              ...rules?.confirmCreatorCode,
              validate: (value) => {
                return getValues("customizeYourCode")
                  ? getValues("customizeYourCode") === value || infoLabels.doNotMatch
                  : false;
              }
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="confirmYourCode"
                errorMessage={error?.message || ""}
                {...field}
                label={pageLabels.confirmYourCode}
                placeholder={pageLabels.confirmYourCode}
                dark
                validated={!disableSubmit}
              />
            )}
          />
        </div>
      </div>
      <Footer
        {...{
          buttons,
          onCancel: onClose,
          disableSubmit: isPending,
          isPending,
          isError: Object.keys(formState.errors).length !== 0 && formState.isDirty
        }}
      />
    </>
  );
});

type CreatorCodeLayout = {
  buttons: {
    yes: string;
    no: string;
    close: string;
  };
};

export type CreatorCodeFormProps = {
  pageLabels: CreatorPageLabels & PageLabels;
  infoLabels: MessageInfoLabels;
  stableDispatch: Dispatch;
  configuration: OnboardingStepConfiguration;
  layout: CreatorCodeLayout;
  errorHandling: ErrorHandling;
  router: NextRouter;
  onClose: MouseEventHandler<HTMLButtonElement>;
  showConfirmation: boolean;
  setShowConfirmation: (show: boolean) => void;
  state: State;
  infoUnionPath: string;
  PROGRAM_CODE: string;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  unhandledError: string;
};

export default memo(function CreatorCodeForm({
  pageLabels,
  infoLabels,
  stableDispatch,
  configuration,
  errorHandling,
  router,
  onClose,
  state,
  showConfirmation,
  setShowConfirmation,
  layout,
  infoUnionPath,
  errorToast,
  unhandledError,
  PROGRAM_CODE
}: CreatorCodeFormProps) {
  const validationService = new ValidateCreatorCodeService(configuration.onBoardingClient);
  const creatorService = configuration.creatorsClient
    ? new CreatorsService(configuration.creatorsClient, configuration.DEFAULT_AVATAR_IMAGE)
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? new CreatorService(configuration.onBoardingClient)
    : undefined;
  const [validationError, setValidationError] = useState("");
  const { isError = false, isValidationError = false, onboardingSteps = [] } = state || {};
  const [creator, setCreator] = useState<CreatorWithProgramCode>();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSubmit: any = useCallback(
    async (data: { confirmYourCode: string }, navigateBack: boolean, navigateToPage: URL): Promise<unknown> => {
      stableDispatch({ type: USER_NAVIGATED, data: false });
      const currentStep = (onboardingSteps as Array<NavStep>).find((step) => step.href === router.pathname);
      setValidationError("");
      const { confirmYourCode } = data;
      try {
        const results = await validationService.validateCreatorCode(confirmYourCode);
        if (!results.isValidCode) {
          setValidationError(infoLabels.alreadyInUse);
          return;
        }
      } catch (e) {
        if (
          (e as ErrorResponse)?.response?.status === 422 &&
          (e as ErrorResponse)?.response?.data?.code === "validate-creator-code-invalid-input"
        ) {
          setValidationError(infoLabels.alreadyInUse);
          return;
        }
        errorHandling(stableDispatch, e as Error | AxiosError);
      }

      try {
        const requestBody = {
          contents: [confirmYourCode]
        };
        const response = await validationService.verifyCreatorCodeContent(requestBody);
        const results = response.results;
        const isHealthy = results.length === 1 && results[0].healthy;
        if (!isHealthy) {
          setValidationError(infoLabels.inappropriateLanguage);
          return;
        }
        if (creator) {
          if (legacyCreatorService) {
            await legacyCreatorService.update({
              accountInformation: {
                ...creator.accountInformation,
                dateOfBirth: LocalizedDate.format(
                  (creator?.accountInformation.dateOfBirth as unknown as LocalizedDate).toDate(),
                  "YYYY-MM-DD"
                ),
                creatorCode: confirmYourCode
              },
              creatorConnectedProgram: PROGRAM_CODE
            } as unknown as Partial<CreatorWithProgramCode>);
          } else {
            await (creatorService as CreatorsService).updateCreator({
              creatorCode: confirmYourCode,
              program: { code: PROGRAM_CODE }
            } as UpdateCreatorRequest);
          }
          stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        }
        if (navigateToPage) {
          router.push(navigateToPage);
        } else if (navigateBack) {
          router.push("/terms-and-conditions");
        } else {
          router.push("/payment-info");
        }
      } catch (e: unknown) {
        if (
          (e as ErrorResponse)?.response?.status === 422 &&
          (e as ErrorResponse)?.response?.data?.code === "check-content-health-invalid-input"
        ) {
          setValidationError(infoLabels.inappropriateLanguage);
          return;
        } else if ((e as ErrorResponse)?.response?.status === 422) {
          // TODO: will be replaced with contentful in next MR.
          const badCodeError = "Unable to validate the creator code. Please try again.";
          setValidationError(badCodeError);
          return;
        }
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    },
    [stableDispatch, creator]
  );

  const handleModalClose = useCallback(() => {
    setShowConfirmation(false);
  }, []);

  const handleCancelRegistration = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);

  useEffect(() => {
    async function fetchData() {
      try {
        stableDispatch({ type: LOADING, data: true });
        const creator = legacyCreatorService
          ? (await legacyCreatorService.getCreatorWithProgramCode()).data
          : await creatorService?.getCreator(PROGRAM_CODE);

        setCreator(creator as unknown as CreatorWithProgramCode);

        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError || toastContent(isValidationError as unknown as ValidationError[])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const cancelRegistrationModalLabels = {
    title: pageLabels.modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: pageLabels.confirmationDesc1,
    confirmationDesc2: pageLabels.confirmationDesc2
  };

  return (
    <>
      <Form mode="onChange" onSubmit={submitHandler}>
        <CreatorCodeFormInput {...{ pageLabels, onClose, infoLabels, isPending, validationError, infoUnionPath }} />
      </Form>
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </>
  );
});
