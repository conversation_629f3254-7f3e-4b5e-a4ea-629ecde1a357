import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import { Button, Icon, rightArrow } from "@eait-playerexp-cn/core-ui-kit";
import { useFormContext } from "react-hook-form";

export type ButtonsProps = {
  cancel: string;
  next?: string;
};

export type FooterProps = {
  buttons: ButtonsProps;
  onCancel?: MouseEventHandler<HTMLButtonElement>;
  disableSubmit: boolean;
  isPending?: boolean;
  isError?: boolean;
  onSave?: MouseEventHandler<HTMLButtonElement>;
};

export default memo(function Footer({
  buttons,
  onCancel,
  disableSubmit,
  isPending,
  isError = false,
  onSave
}: FooterProps) {
  const { formState = { errors: {}, isValid: true } } = useFormContext() || {};
  const hasErrors = Object.keys(formState.errors).length !== 0;

  const onClickProps = {
    ...(onSave ? { onClick: onSave } : {})
  };
  return (
    <div className="ob-footer-container">
      <span className="secondary">
        <Button variant="tertiary" dark size="md" onClick={onCancel}>
          {buttons.cancel}
        </Button>
      </span>
      <span className={isError ? "error-btn" : "primary-btn"}>
        <Button
          {...onClickProps}
          variant="primary"
          size="md"
          type="submit"
          spinner={isPending}
          disabled={hasErrors || formState?.isValid === false || disableSubmit}
        >
          {buttons.next}&nbsp;
          {!isPending && <Icon icon={rightArrow} className="icon" />}
        </Button>
      </span>
    </div>
  );
});
