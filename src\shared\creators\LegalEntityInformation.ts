import { CountryResponse } from "@eait-playerexp-cn/metadata-types";
import Country from "../countries/Country";

export type LegalEntityInformationResponse = {
  entityType: string;
  businessName: string;
  street: string;
  city: string;
  country: CountryResponse;
  state: string;
  zipCode: string;
};

export default class LegalEntityInformation {
  static fromApi(data: LegalEntityInformationResponse): LegalEntityInformation {
    return new LegalEntityInformation(
      data.entityType,
      data.businessName,
      data.street,
      data.city,
      data.country ? Country.fromApi(data.country) : undefined,
      data.state,
      data.zipCode
    );
  }

  constructor(
    readonly entityType?: string,
    readonly businessName?: string,
    readonly street?: string,
    readonly city?: string,
    readonly country?: Country,
    readonly state?: string,
    readonly zipCode?: string
  ) {}
}
