import { Factory } from "fishery";
import Random from "./Random";
import { CreatorType } from "../types";

const factory = Factory.define(() => {
  const creatorType = Random.creatorType();
  return {
    id: creatorType,
    value: creatorType,
    label: Random.creatorTypeLabel(),
    imageAsIcon: "creatorTypes/Youtuber"
  };
});

export function aCreatorType(override = {}): CreatorType {
  return factory.build(override) as CreatorType;
}
