import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import FranchisesYouPlay from "./franchises-you-play";

const meta: Meta<typeof FranchisesYouPlay> = {
  title: "Component Library/Franchises You Play Page",
  component: FranchisesYouPlay,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof FranchisesYouPlay>;

export const FranchisesYouPlayPage: Story = {
  args: {
    analytics: { confirmedFranchise: () => {} },
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: {
      franchisesYouPlay: {
        confirmationDesc1: "confirmationDesc1",
        confirmationDesc2: "conf'irmationDesc2",
        modalConfirmationTitle: "modalConfirmationTitle",
        title: "Franchises you Play",
        description:
          "Tell us about the Electronic Arts franchises that you enjoy playing. This will tell us what opportunities to show you. You can always change them or add more in your profile settings.",
        buttons: {
          yes: "Yes",
          no: "No",
          cancel: "Cancel",
          next: "Next",
          submit: "Submit",
          close: "Close",
          discard: "Discard",
          save: "Save"
        }
      },
      franchisesYouPlayFormLabels: {
        primaryFranchiseTitle: "Franchise you play the most",
        primaryFranchiseSubTitle: "Select the franchise you create content for the most.",
        secondaryFranchiseTitle: "Secondary Franchise",
        secondaryFranchiseSubTitle: "Select the franchise you create content for the most.",
        labels: {
          primaryFranchise: "Primary Franchise",
          loadMore: "Load More"
        },
        messages: {
          primaryFranchise: "primaryFranchise"
        }
      },
      layout: {
        buttons: {
          yes: "Yes",
          no: "No",
          cancel: "Cancel",
          next: "Next",
          submit: "Submit",
          close: "Close",
          discard: "Discard",
          save: "Save"
        },
        main: {
          unhandledError: "unhandledError"
        }
      }
    },
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: () => {},
    dispatch: () => {},
    setShowConfirmation: (_a: boolean) => {},
    showConfirmation: false,
    locale: "en-us",
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
      metadataClient: {
        get: () =>
          Promise.resolve({
            data: [
              {
                value: "a0G7c000006GAZyEAO",
                label: "Iron Man",
                image: "/img/public/franchises/iron-man.png"
              },
              {
                value: "a0G7c000006GAZnEAO",
                label: "Apex Legends",
                image: "/img/public/franchises/05_07_2023_AAA.jpg"
              },
              {
                value: "a0G7c000006GAa8EAG",
                label: "Battlefield",
                image: "/img/public/franchises/battlefield.jpg"
              },
              {
                value: "a0G7c000006GAaEEAW",
                label: "Command and Conquer",
                image: "/img/public/franchises/command-and-conquer.jpg"
              },
              {
                value: "a0G7c000006GAa6EAG",
                label: "Dead Space",
                image: "/img/public/franchises/dead-space.jpg"
              },
              {
                value: "a0G7c000006GAa3EAG",
                label: "DiRT",
                image: "/img/public/franchises/dirt2.png"
              },
              {
                value: "a0G7c000006GAa2EAG",
                label: "Dragon Age",
                image: "/img/public/franchises/dragon-age.jpg"
              },
              {
                value: "a0G7c000006GAaFEAW",
                label: "EA Mobile",
                image: "/img/public/franchises/ea-no-franchise.png"
              },
              {
                value: "a0G7c000006GAaGEAW",
                label: "EA Originals",
                image: "/img/public/franchises/ea-originals.png"
              },
              {
                value: "a0G7c000006GAZwEAO",
                label: "F1",
                image: "/img/public/franchises/f1.jpg"
              },
              {
                value: "a0G7c000006GAZmEAO",
                label: "FIFA",
                image: "/img/public/franchises/fifa.jpg"
              },
              {
                value: "a0G7c000006GAa1EAG",
                label: "GRID",
                image: "/img/public/franchises/grid-legends.jpg"
              },
              {
                value: "a0G7c000006GAZoEAO",
                label: "Madden",
                image: "/img/public/franchises/madden.jpg"
              },
              {
                value: "a0G7c000006GAa7EAG",
                label: "Mass Effect",
                image: "/img/public/franchises/mass-effect.jpg"
              },
              {
                value: "a0G7c000006GAaAEAW",
                label: "NBA",
                image: "/img/public/franchises/nba.jpg"
              },
              {
                value: "a0G7c000006GAZqEAO",
                label: "Need for Speed",
                image: "/img/public/franchises/need-for-speed.jpg"
              },
              {
                value: "a0G7c000006GAaCEAW",
                label: "NHL",
                image: "/img/public/franchises/nhl.png"
              },
              {
                value: "a0G7c000006GAa0EAG",
                label: "PGA Tour",
                image: "/img/public/franchises/pga-tour.jpg"
              },
              {
                value: "a0G7c000006GAa9EAG",
                label: "Plants vs Zombies",
                image: "/img/public/franchises/plants-vs-zombies.jpg"
              },
              {
                value: "a0G7c000006GAa5EAG",
                label: "Real Racing",
                image: "/img/public/franchises/real-racing.jpg"
              },
              {
                value: "a0G7c000006GAa4EAG",
                label: "Skate",
                image: "/img/public/franchises/skate.jpg"
              },
              {
                value: "a0G7c000006GAZxEAO",
                label: "Star Wars",
                image: "/img/public/franchises/star-wars.jpg"
              },
              {
                value: "a0G7c000006GAZpEAO",
                label: "The Sims",
                image: "/img/public/franchises/the-sims.jpg"
              },
              {
                value: "a0G7c000006GAaDEAW",
                label: "UFC",
                image: "/img/public/franchises/ufc.png"
              }
            ]
          }),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      onBoardingClient: {} as unknown as TraceableHttpClient
    }
  }
};
