import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect } from "react";
import { Button, Icon, Input, MultiSelect, outlineAdd, Select } from "@eait-playerexp-cn/core-ui-kit";
import { Language, Locale } from "@eait-playerexp-cn/metadata-types";
import { Labels } from "../CommunicationPreferencesStep";
import CommunicationPreferences from "../../shared/creators/CommunicationPreferences";
import CreatorForm from "../../utils/CreatorForm";
import { POPUP_OPENED, WINDOW_PARAMS } from "../../utils";
import ConnectedAccount, { ConnectAccounts, ConnectedAccountComponent } from "../ConnectedAccount/ConnectedAccounts";
import { Dispatch, ErrorHandling, OnboardingStepConfiguration, State } from "../../types";

export type AddDiscordAccountProps = {
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  labels: Labels;
  setRemoveAccount: (removeAccount: boolean) => void;
  removeAccount: boolean | null;
  discord: { name: string; tag: string } | null;
  accountToRemove: string | boolean;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  stableDispatch: Dispatch;
  state: State;
  configuration: OnboardingStepConfiguration;
  errorHandling: ErrorHandling;
  connectAccounts: ConnectAccounts;
};

export const AddDiscordAccount = memo(function AddDiscordAccount({
  showAddConfirmation,
  setShowAddConfirmation,
  labels,
  discord,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  stableDispatch,
  state,
  configuration,
  errorHandling,
  connectAccounts
}: AddDiscordAccountProps) {
  const { translation } = labels;
  const handleClick = useCallback(() => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      const loginWindow = window.open(connectAccounts[0].redirectUrl, "_blank", WINDOW_PARAMS);
      stableDispatch({ type: POPUP_OPENED, data: true });
      const loop = setInterval(function () {
        if (loginWindow?.closed) {
          clearInterval(loop);
          stableDispatch({ type: POPUP_OPENED, data: false });
        }
      }, 100);
    }
  }, [showAddConfirmation, stableDispatch, setShowAddConfirmation]);

  return (
    <div className="onboarding-mg-communication-row discord">
      <h4 className="onboarding-mg-communication-title discord">{translation.labels.discordTitle}</h4>
      <div className="onboarding-mg-communication-description discord">{translation.labels.discordDescription}</div>
      {(discord && (
        <ConnectedAccountComponent
          labels={labels}
          setAccountToRemove={setAccountToRemove}
          accountToRemove={accountToRemove as string}
          setShowAddConfirmation={setShowAddConfirmation}
          showAddConfirmation={showAddConfirmation}
          stableDispatch={stableDispatch}
          setShowRemoveAccountModal={setShowRemoveAccountModal}
          showRemoveAccountModal={showRemoveAccountModal}
          errorHandling={errorHandling}
          state={state}
          configuration={configuration}
          connectAccounts={connectAccounts}
          accounts={[{ ...discord, type: "DISCORD", username: discord.tag }] as unknown as Array<ConnectedAccount>}
        />
      )) || (
        <Button size="sm" onClick={handleClick}>
          <Icon icon={outlineAdd} />
          <span>{translation.labels.addDiscord}</span>
        </Button>
      )}
    </div>
  );
});

export type CommunicationPreferencesInputProps = {
  languages: Language[];
  locales: Locale[];
  communications: CommunicationPreferences;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  labels: Labels;
  removeAccount: boolean | null;
  setRemoveAccount: (account: boolean) => void;
  accountToRemove: string | boolean;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  stableDispatch: Dispatch;
  errorHandling: ErrorHandling;
  state: State;
  configuration: OnboardingStepConfiguration;
  connectAccounts: ConnectAccounts;
};

const CommunicationPreferencesInput = memo(function CommunicationPreferencesInput({
  languages,
  locales,
  communications,
  showAddConfirmation,
  setShowAddConfirmation,
  labels,
  removeAccount,
  setRemoveAccount,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  stableDispatch,
  errorHandling,
  state,
  configuration,
  connectAccounts
}: CommunicationPreferencesInputProps) {
  const methods = useFormContext();
  const { control, setValue } = methods;
  const { discord = null } = communications || {};
  const { translation } = labels;
  const rules = CreatorForm.communicationRules(translation);
  useEffect(() => {
    setValue("preferredLanguage", (communications && communications.preferredLanguage) || locales[0]);
  }, [locales]);

  return (
    <>
      <AddDiscordAccount
        setShowAddConfirmation={setShowAddConfirmation}
        setAccountToRemove={setAccountToRemove}
        accountToRemove={accountToRemove}
        showAddConfirmation={showAddConfirmation}
        setRemoveAccount={setRemoveAccount}
        showRemoveAccountModal={showRemoveAccountModal}
        removeAccount={removeAccount}
        discord={discord as unknown as { name: string; tag: string }}
        labels={labels}
        stableDispatch={stableDispatch}
        setShowRemoveAccountModal={setShowRemoveAccountModal}
        errorHandling={errorHandling}
        state={state}
        configuration={configuration}
        connectAccounts={connectAccounts}
      />
      <div className="onboarding-mg-communication-row preferred-email">
        <h4 className="onboarding-mg-communication-title preferred-email">
          {translation.labels.preferredEmailAddressTitle}
        </h4>
        <div className="onboarding-mg-communication-description preferred-email">
          {translation.labels.preferredEmailAddressDescription}
        </div>
        <div className="onboarding-mg-communication-preference-field">
          <Controller
            control={control}
            name="email"
            rules={rules.preferredEmail}
            defaultValue={communications.email}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="creator-preferred-email"
                errorMessage={(error && error.message) || ""}
                {...field}
                label={translation.labels.preferredEmail}
                placeholder={translation.labels.preferredEmail}
              />
            )}
          />
        </div>
      </div>
      <div className="onboarding-mg-communication-row preferred-phone-number">
        <h4 className="onboarding-mg-communication-title preferred-phone-number">
          {translation.labels.preferredPhoneNumberTitle}
        </h4>
        <div className="onboarding-mg-communication-preference-field">
          <Controller
            control={control}
            name="phone"
            rules={{
              ...rules.preferredPhoneNumber
            }}
            defaultValue={communications.phone}
            render={({ field, fieldState: { error, isTouched } }) => (
              <Input
                id="creator-preferred-phone-number"
                errorMessage={(isTouched && error && error.message) || ""}
                {...field}
                label={translation.labels.preferredPhoneNumber}
                placeholder={translation.labels.preferredPhoneNumber}
              />
            )}
          />
        </div>
      </div>
      <div className="onboarding-mg-communication-row content-language">
        <h4 className="onboarding-mg-communication-title content-language">
          {translation.labels.contentLanguagesTitle}
        </h4>
        <div className="onboarding-mg-communication-description content-language">
          {translation.labels.contentLanguagesDescription}
        </div>
        <Controller
          control={control}
          name="contentLanguages"
          rules={rules.contentLanguage}
          defaultValue={communications.contentLanguages}
          render={({ field, fieldState: { error, isTouched } }) => (
            <MultiSelect
              {...field}
              selectedOptions={field.value}
              options={languages}
              errorMessage={(isTouched && error && error.message) || ""}
              label={translation.labels.contentLanguage}
              placeholder={translation.labels.contentLanguage}
            />
          )}
        />
      </div>
      <div className="onboarding-mg-communication-row language">
        <h4 className="onboarding-mg-communication-title language">{translation.labels.languageTitle}</h4>
        <div className="onboarding-mg-communication-description language">{translation.labels.languageDescription}</div>
        <Controller
          control={control}
          name="preferredLanguage"
          rules={rules.language}
          render={({ field, fieldState: { error, isTouched } }) => (
            <Select
              id="creator-preferred-language"
              selectedOption={(communications && communications.preferredLanguage) || ([] as unknown as Language)}
              errorMessage={(isTouched && error && error.message) || ""}
              options={locales}
              label={translation.labels.language}
              onChange={(item) => {
                field.onChange(item);
              }}
              dark
            />
          )}
        />
      </div>
    </>
  );
});

export default CommunicationPreferencesInput;
