import React, { createContext, FunctionComponent, ReactElement, ReactNode, useReducer } from "react";
import { render, RenderResult } from "@testing-library/react";
import { withToastProvider } from "@eait-playerexp-cn/core-ui-kit";

export type AppProps = {
  [key: string]: unknown;
};

export type AppWrapperProps = {
  children: ReactNode;
};

const initialState = {};
const reducer = (state: typeof initialState, action: { type: string }) => {
  switch (action.type) {
    default:
      return state;
  }
};

const AppContext = createContext({ state: initialState, dispatch: (_action: { type: string }) => {} });

function AppWrapper({ children }: AppWrapperProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  return <AppContext.Provider value={{ state, dispatch }}>{children}</AppContext.Provider>;
}

export const renderPage = (component: ReactElement): RenderResult => {
  const DummyApp: FunctionComponent<AppProps> = withToastProvider(() => component);

  return render(
    <AppWrapper>
      <DummyApp />
    </AppWrapper>
  );
};
