import { Factory } from "fishery";
import Random from "./Random";
import { FranchiseResponse } from "../types";

const factory = Factory.define<FranchiseResponse>(() => {
  const franchise = Random.franchise();
  return {
    name: franchise,
    id: Random.uuid(),
    type: Random.franchiseType(),
    boxArtUrl: Random.imageUrl(),
    label: franchise,
    value: Random.uuid()
  };
});

export function aPrimaryFranchise(override = {}): FranchiseResponse {
  return factory.build({ type: "PRIMARY", ...override });
}

export function aSecondaryFranchise(override = {}): FranchiseResponse {
  return factory.build({ type: "SECONDARY", ...override });
}
