import { Factory } from "fishery";
import Random from "../Random";
import { PreferredPlatformResponse } from "src/shared/platforms/PreferredPlatform";

const factory = Factory.define<PreferredPlatformResponse>(() => {
  const name = Random.platform();
  return {
    name,
    code: name.toLowerCase(),
    id: Random.uuid(),
    type: Random.platformType()
  };
});

export function aPrimaryPlatform(override = {}): PreferredPlatformResponse {
  return factory.build({ ...override, type: "PRIMARY" });
}

export function aSecondaryPlatform(override = {}): PreferredPlatformResponse {
  return factory.build({ ...override, type: "SECONDARY" });
}

export function playstation(): PreferredPlatformResponse {
  return factory.build({ id: "playstation", name: "playstation" });
}

export function xbox(): PreferredPlatformResponse {
  return factory.build({ id: "xbox", name: "xbox" });
}
