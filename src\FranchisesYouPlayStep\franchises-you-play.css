.onboarding-creator-franchises-container {
  @apply w-full xs:mt-meas20 mb-[70px];
}
.onboarding-creator .mg-franchises-you-play {
  @apply m-meas8 sm:m-auto;
}
.onboarding-creator .franchises-you-play-form {
  @apply mx-meas8 sm:m-auto;
}
.onboarding-creator-franchises-container .search-label {
  @apply hidden;
}
.onboarding-creator .mg-page-onboarding-creator form {
  @apply w-[100%];
}

.mg-primary-franchise-title {
  @apply pb-meas10 text-center font-display-bold  xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.mg-primary-franchise-subtitle {
  @apply mb-meas16 text-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-primary-franchise-container {
  @apply my-meas16 flex w-full flex-col items-center justify-center border-b border-t border-gray-10 border-opacity-[0.33] py-meas16 md:w-[640px];
}
.mg-primary-franchise-container .select-box {
  @apply w-[317px];
}
.mg-sc-franchise-container {
  @apply flex w-full flex-col items-center justify-center overflow-hidden xl:w-[1070px];
}
.primary-franchise-selected {
  border: 4px solid rgba(115, 204, 117) !important;
  border-radius: 4px;
}
.primary-franchise-option {
  @apply mt-meas12;
}
.secondary-franchise-options {
  @apply grid grid-cols-2 gap-meas8 md:grid-cols-4 md:gap-meas10 xl:gap-meas6;
}
.secondary-franchise-checkbox-card-container {
  @apply max-h-[180px] md:min-w-[150px] xl:max-h-[280px] xl:min-w-[240px];
}
.primary-franchise-option-image {
  @apply h-[200px] w-[200px] md:h-[230px] md:w-[230px] xl:h-[318px] xl:w-[318px];
}
.secondary-franchise-load-more {
  @apply mb-meas32 mt-meas12 md:mt-meas12 xl:mt-meas20;
}
.mg-secondary-franchise-disabled {
  opacity: 0.2;
}
.mg-page-onboarding-creator .onboarding-creator-franchises-container form {
  @apply m-auto w-[100%];
}
.onboarding-creator-franchises-container .card-title {
  @apply xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small xl:text-body-default;
}

.franchises-you-play-form {
  @apply grid grid-cols-1;
}
.franchises-you-play {
  @apply flex flex-1 flex-col items-center;
}
.mg-franchises-you-play {
  @apply m-meas8 text-center md:w-[630px];
}
.mg-franchises-you-play-title {
  @apply pb-meas10 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10;
}
.mg-franchises-you-play-description {
  @apply font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10;
}
