import { type TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import ValidateCreatorCodeService from "./ValidateCreatorCodeService";

jest.mock("@eait-playerexp-cn/http-client");

describe("ValidateCreatorCodeService", () => {
  it("verifies creator code content", async () => {
    const scannedResultsResponse = {
      results: [
        {
          content: "1234",
          healthy: true
        }
      ]
    };
    const client = {
      post: jest.fn().mockResolvedValue({
        data: scannedResultsResponse
      })
    } as unknown as TraceableHttpClient;
    const service = new ValidateCreatorCodeService(client);
    const creatorCodeContentBody = { contents: ["1234"] };

    const scannedResult = await service.verifyCreatorCodeContent(creatorCodeContentBody);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith(`/api/verify-creator-code`, { body: creatorCodeContentBody });
    expect(scannedResult).toEqual(scannedResultsResponse);
  });

  it("validates the creator code", async () => {
    const creatorCode = "5678";
    const codeResponse = {
      creatorCode,
      isValidCode: false
    };
    const client = {
      get: jest.fn().mockResolvedValue({
        data: codeResponse
      })
    } as unknown as TraceableHttpClient;
    const service = new ValidateCreatorCodeService(client);

    const creatorCodeData = await service.validateCreatorCode(creatorCode);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/api/v2/creator-codes/${creatorCode}`);
    expect(creatorCodeData).toEqual(codeResponse);
  });
});
