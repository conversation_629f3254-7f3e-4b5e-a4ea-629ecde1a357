import React, { ReactElement } from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe, toHaveNoViolations } from "jest-axe";
import { FormProvider, useForm } from "react-hook-form";
import PrimaryPlatform from "./PrimaryPlatform";
import { InformationLabels } from "../../Translations/Information";
import { PlatformOption } from "../../types";
import { selectOption } from "../../Helpers/Forms";

describe("PrimaryPlatform", () => {
  const platformOptions: PlatformOption[] = [
    {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    },
    {
      value: "playstation",
      label: "PlayStation",
      imageAsIcon: "/img/platforms/playstation.png"
    },
    {
      value: "nintendo",
      label: "Nintendo Switch",
      imageAsIcon: "/img/platforms/nintendo.png"
    }
  ];

  const labels = {
    infoLabels: {
      interestedCreatorTitle: InformationLabels.infoLabels.interestedCreatorTitle,
      messages: {
        ...InformationLabels.infoLabels.messages,
        primaryPlatform: InformationLabels.infoLabels.messages.primaryPlatform
      }
    },
    translation: InformationLabels.translation,
    formLabels: {
      ...InformationLabels.formLabels,
      labels: InformationLabels.formLabels,
      platformPreferences: InformationLabels.formLabels.platformPreferences,
      platformPreferencesTitle: InformationLabels.formLabels.platformPreferencesTitle,
      secondaryPlatforms: InformationLabels.formLabels.secondaryPlatforms,
      secondaryPlatformsTitle: InformationLabels.formLabels.secondaryPlatformsTitle
    },
    pageLabels: {
      ...InformationLabels.pageLabels,
      primaryPlatform: InformationLabels.pageLabels.primaryPlatform
    },
    layout: InformationLabels.layout
  };

  const primaryPlatformProps = {
    primaryPlatform: null,
    secondaryPlatforms: [],
    options: platformOptions,
    labels: labels
  };

  // Wrapper component to provide form context
  const FormWrapper = ({ children }: { children: ReactElement }) => {
    const methods = useForm({
      defaultValues: {
        primaryPlatform: null,
        secondaryPlatforms: []
      }
    });
    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows platform preferences section with correct labels", () => {
    render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );

    const platformTitle = screen.getByRole("heading", { name: labels.formLabels.platformPreferences });
    expect(platformTitle).toBeInTheDocument();
    expect(platformTitle).toHaveClass("onboarding-mg-platform-title");
    expect(screen.getByLabelText(labels.pageLabels.primaryPlatform)).toBeInTheDocument();
  });

  it("shows secondary platforms section with correct labels", () => {
    render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );

    const secondaryTitle = screen.getByRole("heading", { name: labels.formLabels.secondaryPlatforms });
    expect(secondaryTitle).toBeInTheDocument();
    expect(secondaryTitle).toHaveClass("onboarding-mg-secondary-title");
  });

  it("initializes with first platform option when no primary platform is provided", () => {
    render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );

    const platformImage = screen.getByAltText("Platform image");
    expect(platformImage).toHaveAttribute("src", platformOptions[0].imageAsIcon);
  });

  it("initializes with provided primary platform", () => {
    const propsWithPrimaryPlatform = {
      ...primaryPlatformProps,
      primaryPlatform: platformOptions[1]
    };
    render(
      <FormWrapper>
        <PrimaryPlatform {...propsWithPrimaryPlatform} />
      </FormWrapper>
    );

    const platformImage = screen.getByAltText("Platform image");
    expect(platformImage).toHaveAttribute("src", platformOptions[1].imageAsIcon);
  });

  it("updates platform image when primary platform is changed", async () => {
    const { container } = render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );
    let platformImage = screen.getByAltText("Platform image");
    expect(platformImage).toHaveAttribute("src", platformOptions[0].imageAsIcon);

    await selectOption({
      option: platformOptions[1].label,
      container,
      label: labels.pageLabels.primaryPlatform
    });

    platformImage = screen.getByAltText("Platform image");
    expect(platformImage).toHaveAttribute("src", platformOptions[1].imageAsIcon);
  });

  it("disables secondary platforms when no primary platform is selected", () => {
    render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );
    const secondaryTitle = screen.getByRole("heading", { name: labels.formLabels.secondaryPlatforms });
    const secondaryDescription = document.querySelector(".onboarding-mg-platform-description[data-disabled]");

    expect(secondaryTitle).toHaveAttribute("data-disabled", "false");
    expect(secondaryDescription).toHaveAttribute("data-disabled", "false");
  });

  it("enables secondary platforms when primary platform is selected", async () => {
    const { container } = render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );

    await selectOption({
      option: platformOptions[1].label,
      container,
      label: labels.pageLabels.primaryPlatform
    });

    const secondaryTitle = screen.getByRole("heading", { name: labels.formLabels.secondaryPlatforms });
    const secondaryDescription = document.querySelector(".onboarding-mg-platform-description[data-disabled]");

    expect(secondaryTitle).toHaveAttribute("data-disabled", "false");
    expect(secondaryDescription).toHaveAttribute("data-disabled", "false");
  });

  it("shows secondary platforms with provided values", () => {
    const propsWithSecondaryPlatforms = {
      ...primaryPlatformProps,
      primaryPlatform: platformOptions[0],
      secondaryPlatforms: [platformOptions[1], platformOptions[2]]
    };
    render(
      <FormWrapper>
        <PrimaryPlatform {...propsWithSecondaryPlatforms} />
      </FormWrapper>
    );

    const checkboxCards = screen.getAllByRole("checkbox");
    expect(checkboxCards.length).toBeGreaterThan(0);
  });

  it("shows validation error when primary platform is required but not selected", async () => {
    const FormWrapperWithValidation = ({ children }: { children: ReactElement }) => {
      const methods = useForm({
        defaultValues: {
          primaryPlatform: null,
          secondaryPlatforms: []
        },
        mode: "onChange"
      });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };
    render(
      <FormWrapperWithValidation>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapperWithValidation>
    );
    const select = screen.getByLabelText(labels.pageLabels.primaryPlatform);

    await userEvent.click(select);
    await userEvent.tab();

    await waitFor(() => {
      expect(screen.queryByText(labels.infoLabels.messages.primaryPlatform)).toBeInTheDocument();
    });
  });

  it("handles empty options array gracefully", () => {
    const propsWithEmptyOptions = {
      ...primaryPlatformProps,
      options: []
    };
    render(
      <FormWrapper>
        <PrimaryPlatform {...propsWithEmptyOptions} />
      </FormWrapper>
    );

    const platformImage = screen.getByAltText("Platform image");
    expect(platformImage).toHaveAttribute("src", "/img/default-platform.png");
  });

  it.each(platformOptions)("shows platform option '$label' in the select dropdown", async (platformOption) => {
    const user = userEvent.setup();
    const { container } = render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );
    const select = screen.getByLabelText(labels.pageLabels.primaryPlatform);

    await user.click(select);

    await waitFor(() => {
      expect(container.querySelector(".select-list")).toBeInTheDocument();
    });
    expect(screen.getByRole("button", { name: platformOption.label })).toBeInTheDocument();
  });

  it("shows secondary platform checkboxes for all options", () => {
    const propsWithPrimaryPlatform = {
      ...primaryPlatformProps,
      primaryPlatform: platformOptions[0]
    };
    render(
      <FormWrapper>
        <PrimaryPlatform {...propsWithPrimaryPlatform} />
      </FormWrapper>
    );

    const checkboxes = screen.getAllByRole("checkbox");
    expect(checkboxes).toHaveLength(platformOptions.length);
  });

  it("is accessible", async () => {
    expect.extend(toHaveNoViolations);
    const { container } = render(
      <FormWrapper>
        <PrimaryPlatform {...primaryPlatformProps} />
      </FormWrapper>
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
