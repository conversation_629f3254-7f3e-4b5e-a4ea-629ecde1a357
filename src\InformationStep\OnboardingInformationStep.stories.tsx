import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import OnboardingInformationStep, { Labels, OnboardingInformationStepProps } from "./OnboardingInformationStep";
import { InformationLabels } from "../Translations/Information";

const meta: Meta<typeof OnboardingInformationStep> = {
  title: "Component Library/Onboarding Information Step Page",
  component: OnboardingInformationStep,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof OnboardingInformationStep>;

export const OnboardingInformationStepPage: Story = {
  args: {
    analytics: {},
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: InformationLabels as Labels,
    stableDispatch: () => {},
    PROGRAM_CODE: "",
    router: {} as NextRouter,
    errorHandling: () => {},
    registrationCode: "",
    configuration: {
      onBoardingClient: {} as TraceableHttpClient,
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
      metadataClient: {} as TraceableHttpClient,
      creatorsClient: undefined,
      navigateToNextPage: "",
      formFields: {
        preferredName: { required: false },
        preferredPronoun: { required: true },
        preferredPronouns: { required: false },
        firstName: { required: true },
        lastName: { required: true },
        dateOfBirth: { required: true },
        country: { required: true },
        street: { required: true },
        city: { required: true },
        state: { required: true },
        zipCode: { required: true },
        tShirtSize: { required: true },
        entityType: { required: true },
        businessName: { required: true },
        email: { required: true },
        url: { required: true },
        followers: { required: true },
        EAID: { required: true },
        eaEmailID: { required: true },
        preferredPlatforms: { required: false },
        socialLinks: { required: false },
        profilePicture: { required: false }
      }
    },
    locale: "en",
    showConfirmation: false,
    setShowConfirmation: () => {},
    onClose: () => {},
    errorToast: () => {},
    FLAG_COUNTRIES_BY_TYPE: false,
    isExistingCreator: false,
    futureCreator: {},
    user: {},
    isPreferredField: true,
    preferredPronounsOptions: [
      { value: "He / Him", label: "He / Him" },
      { value: "She / Her", label: "She / Her" },
      { value: "They / Them", label: "They / Them" },
      { value: "Other (Please specify)", label: "Other (Please specify)" },
      { value: "Prefer not to say", label: "Prefer not to say" }
    ]
  } as OnboardingInformationStepProps
};
