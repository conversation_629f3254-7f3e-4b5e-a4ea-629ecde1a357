import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CommunicationPreferencesStep, { Labels } from "./CommunicationPreferencesStep";
import { ConnectAccountLabels } from "src/Translations/ConnectAccounts";

const meta: Meta<typeof CommunicationPreferencesStep> = {
  title: "Component Library/Communication Preferences",
  component: CommunicationPreferencesStep,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CommunicationPreferencesStep>;

export const CommunicationPreferencesStepPage: Story = {
  args: {
    labels: {
      ...ConnectAccountLabels,
      layout: {
        main: {
          unhandledError: "Unhandled Error"
        },
        buttons: {
          yes: "Yes",
          no: "No",
          close: "Close",
          cancel: "Cancel",
          discard: "Discard",
          save: "Save",
          next: "Next"
        }
      },
      translation: {
        messages: {
          preferredEmailTooLong: "Preferred email is too long",
          preferredEmailInvalid: "Preferred email is invalid",
          preferredPhoneNumber: "Preferred phone number is invalid",
          preferredPhoneNumberTooLong: "Preferred phone number is too long",
          contentLanguage: "Content language is required",
          language: "Language is required",
          preferredEmail: "Preferred email is required"
        },
        labels: {
          preferredEmail: "Preferred Email",
          preferredEmailAddressTitle: "Preferred Email Address",
          preferredEmailAddressDescription: "Please enter your preferred email address.",
          preferredPhoneNumberTitle: "Preferred Phone Number",
          preferredPhoneNumberDescription: "Please enter your preferred phone number.",
          preferredPhoneNumber: "Preferred Phone Number",
          contentLanguageTitle: "Content Language",
          contentLanguage: "Content Language",
          languageTitle: "Language",
          contentLanguagesDescription: "Please select your preferred content languages.",
          language: "Language",
          languageDescription: "Please select your preferred language.",
          contentLanguagesTitle: "Content Languages",
          discordTitle: "Discord",
          discordDescription: "Please enter your Discord username.",
          addDiscord: "Add Discord"
        },
        confirmationDesc1: "Confirmation Description 1",
        confirmationDesc2: "Confirmation Description 2",
        modalConfirmationTitle: "Modal Confirmation Title",
        title: "Communication Preferences",
        description: "Please select your preferred communication methods."
      },
      breadcrumbLabels: {
        modalTitle: "Modal Title",
        modalMessage: "Modal Message"
      }
    } as unknown as Labels,
    analytics: {
      completedOnboardingFlow: () => {}
    },
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
      metadataClient: {
        get: () =>
          Promise.resolve({
            data: [
              {
                key: "language",
                value: "en-us"
              }
            ]
          })
      } as unknown as TraceableHttpClient,
      onBoardingClient: {
        get: () =>
          Promise.resolve({
            data: {
              email: "",
              phone: "",
              discord: ""
            }
          })
      } as unknown as TraceableHttpClient,
      programCode: "the_sims"
    },
    errorHandling: () => {},
    stableDispatch: () => {},
    navigateToPage: "/payment"
  }
};
