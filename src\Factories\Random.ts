import { faker } from "@faker-js/faker";

const { image, string, location, helpers, internet, person, number, datatype, phone, company } = faker;

export default class Random {
  static imageUrl(): string {
    return image.urlLoremFlickr();
  }

  static string(): string {
    return string.alphanumeric();
  }

  static fullName(): string {
    return person.fullName();
  }

  static tShirtSize(): string {
    return helpers.arrayElement(["S", "M", "XL", "XXL"]);
  }

  static phone(): string {
    return phone.number();
  }

  static streetAddress(): string {
    return location.streetAddress();
  }

  static zipCode(): string {
    return location.zipCode();
  }

  static companyName(): string {
    return company.name();
  }

  static country(): string {
    return location.country();
  }

  static platformType(): string {
    return helpers.arrayElement(["PRIMARY", "SECONDARY"]);
  }

  static platform(): string {
    return helpers.arrayElement(["Nintendo Switch", "XBox", "PlayStation", "Mobile", "Mac"]);
  }

  static entityType(): string {
    return helpers.arrayElement(["INDIVIDUAL", "BUSINESS"]);
  }

  static userStatus(): string {
    return helpers.arrayElement(["UNREGISTERED", "INACTIVE", "ACTIVE"]);
  }

  static avatar(): string {
    return image.avatar();
  }

  static accountType(): string {
    return helpers.arrayElement(["TWITCH", "FACEBOOK", "INSTAGRAM", "YOUTUBE", "TIKTOK"]);
  }

  static state(): string {
    return location.state();
  }

  static city(): string {
    return location.city();
  }

  static countryCode(): string {
    return location.countryCode();
  }

  static creatorTypeLabel(): string {
    return helpers.arrayElement(["Youtuber", "Live Streamer", "Podcaster", "Blogger"]);
  }

  static creatorType(): string {
    return helpers.arrayElement(["YOUTUBER", "LIVE_STREAMER", "PODCASTER", "BLOGGER"]);
  }

  static franchiseType(): string {
    return helpers.arrayElement(["PRIMARY", "SECONDARY"]);
  }

  static franchise(): string {
    return helpers.arrayElement(["Need for Speed", "Apex Legends", "Madden NFL", "The Sims", "FIFA"]);
  }

  static boolean(): boolean {
    return datatype.boolean();
  }

  static uuid(): string {
    return string.uuid();
  }

  static locale(): string {
    return helpers.arrayElement(["en_US", "fr_CA", "es_ES", "de_DE", "it_IT", "ja_JP", "ko_KR", "pt_BR", "zh_CN"]);
  }

  static url(): string {
    return image.url();
  }

  static email(): string {
    return internet.email();
  }

  static firstName(): string {
    return person.firstName();
  }

  static lastName(): string {
    return person.lastName();
  }

  static nucleusId(): number {
    return number.int();
  }

  static number(min?: number, max?: number): number {
    return number.int({ min, max });
  }

  static userName(): string {
    return internet.userName();
  }

  static programCode(): string {
    return helpers.arrayElement(["affiliate", "creator_network", "the_sims"]);
  }
}
