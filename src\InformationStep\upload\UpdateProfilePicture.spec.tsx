import React from "react";
import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe, toHaveNoViolations } from "jest-axe";
import { UpdateProfilePicture } from "./UpdateProfilePicture";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { renderPage } from "../../Helpers/Page";
import { InformationLabels } from "../../Translations/Information";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

global.URL.createObjectURL = jest.fn(() => "mocked-url");
global.URL.revokeObjectURL = jest.fn();
jest.mock("next/router", () => ({
  useRouter: () => ({
    locale: "en-us"
  })
}));
jest.mock("@eait-playerexp-cn/creators-http-client");

describe("UpdateProfilePicture", () => {
  const labels = {
    buttons: {
      browse: InformationLabels.formLabels.browse,
      save: InformationLabels.pageLabels.save,
      cancel: InformationLabels.formLabels.cancel,
      close: InformationLabels.formLabels.close
    },
    profilePicture: {
      title: InformationLabels.formLabels.profilePicture.title,
      avatarRequired: InformationLabels.formLabels.profilePicture.avatarRequired,
      avatarInvalid: InformationLabels.formLabels.profilePicture.avatarInvalid,
      avatarMoreThanLimit: InformationLabels.formLabels.profilePicture.avatarMoreThanLimit,
      message: InformationLabels.formLabels.profilePicture.message,
      termsAndConditionsFirst: InformationLabels.formLabels.profilePicture.termsAndConditionsFirst,
      termsAndConditionsMiddle: InformationLabels.formLabels.profilePicture.termsAndConditionsMiddle,
      termsAndConditionsLast: InformationLabels.formLabels.profilePicture.termsAndConditionsLast
    },
    profileLabels: { updateAvatar: "Update Avatar" }
  };
  const user = { avatar: "https://example.com/avatar.jpg" };
  const dispatch = jest.fn();
  const configuration = {
    onBoardingClient: {} as TraceableHttpClient,
    metadataClient: {} as TraceableHttpClient,
    creatorsClient: {} as TraceableHttpClient,
    DEFAULT_AVATAR_IMAGE: "https://example.com/default.jpg"
  };
  const updateProfilePictureProps = {
    src: "https://example.com/avatar.jpg",
    labels: labels,
    user: user,
    stableDispatch: dispatch,
    configuration
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows avatar with edit button", () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    expect(screen.getByRole("presentation")).toHaveAttribute("src", "https://example.com/avatar.jpg");
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });

  it("displays profile picture interface for users to update their avatar", () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    // Should show the profile picture with edit functionality
    expect(screen.getByRole("presentation")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });

  it("opens modal when edit button is clicked", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    expect(screen.getByText(labels.profilePicture.title)).toBeInTheDocument();
    expect(screen.getByText(labels.profilePicture.message)).toBeInTheDocument();
  });

  it("closes modal when cancel button is clicked", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.cancel }));

    expect(screen.queryByText(labels.profilePicture.title)).not.toBeInTheDocument();
  });

  it("validates file is required", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.save }));

    expect(screen.getByRole("button", { name: labels.buttons.save })).toBeDisabled();
  });

  it("validates file type", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const invalidFile = new File(["content"], "test.txt", { type: "text/plain" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, invalidFile);

    expect(screen.getByText(labels.profilePicture.avatarInvalid)).toBeInTheDocument();
  });

  it("validates file size", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} size={100} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const largeFile = new File(["x".repeat(200)], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, largeFile);

    expect(screen.getByText(labels.profilePicture.avatarMoreThanLimit)).toBeInTheDocument();
  });

  it("accepts valid image file", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const validFile = new File(["content"], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, validFile);

    expect(screen.queryByText(labels.profilePicture.avatarInvalid)).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: labels.buttons.save })).not.toBeDisabled();
  });

  it("disables save button when no file selected", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    expect(screen.getByRole("button", { name: labels.buttons.save })).toBeDisabled();
  });

  it("submits form successfully", async () => {
    const mockUpdateAvatar = jest.fn().mockResolvedValue({});
    (CreatorsService as jest.Mock).mockImplementation(() => ({
      updateAvatar: mockUpdateAvatar
    }));
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    const validFile = new File(["content"], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, validFile);
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.save }));

    await waitFor(() => {
      expect(mockUpdateAvatar).toHaveBeenCalledWith(expect.any(FormData));
    });
  });

  it("handles API error", async () => {
    const mockUpdateAvatar = jest.fn().mockRejectedValue({
      response: { status: 422, data: { errors: { avatar: "Upload failed" } } }
    });
    (CreatorsService as jest.Mock).mockImplementation(() => ({
      updateAvatar: mockUpdateAvatar
    }));
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    const validFile = new File(["content"], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, validFile);
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.save }));

    await waitFor(() => {
      expect(screen.getByText("Upload failed")).toBeInTheDocument();
    });
  });

  it("updates user avatar after successful upload", async () => {
    const mockUpdateAvatar = jest.fn().mockResolvedValue({});
    (CreatorsService as jest.Mock).mockImplementation(() => ({
      updateAvatar: mockUpdateAvatar
    }));
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const validFile = new File(["content"], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, validFile);
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.save }));

    await waitFor(() => {
      expect(dispatch).toHaveBeenCalledWith({
        type: "SESSION_USER",
        data: expect.objectContaining({
          avatar: expect.stringMatching(/https:\/\/example\.com\/avatar\.jpg\?t=\d+/)
        })
      });
    });
  });

  it("shows terms and conditions link", async () => {
    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const termsLink = screen.getByRole("link", { name: labels.profilePicture.termsAndConditionsMiddle });
    expect(termsLink).toHaveAttribute("href", "https://tos.ea.com/legalapp/WEBTERMS/US/en/PC/");
  });

  it("handles missing creator service gracefully", async () => {
    const configWithoutClient = { ...configuration, creatorsClient: undefined };

    renderPage(<UpdateProfilePicture {...updateProfilePictureProps} configuration={configWithoutClient} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const validFile = new File(["content"], "test.jpg", { type: "image/jpeg" });
    const fileInput = screen.getByLabelText(labels.buttons.browse);
    await userEvent.upload(fileInput, validFile);
    await userEvent.click(screen.getByRole("button", { name: labels.buttons.save }));

    expect(screen.queryByText("Upload failed")).not.toBeInTheDocument();
  });

  it("is accessible", async () => {
    expect.extend(toHaveNoViolations);
    const { container } = renderPage(<UpdateProfilePicture {...updateProfilePictureProps} />);
    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
