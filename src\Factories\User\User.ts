import { Factory } from "fishery";
import Random from "../Random";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { User } from "@eait-playerexp-cn/server-kernel";

const factory = Factory.define(() => ({
  id: Random.uuid(),
  analyticsId: Random.uuid(),
  needsMigration: Random.boolean(),
  nucleusId: Random.nucleusId(),
  username: Random.userName(),
  originEmail: Random.email(),
  status: Random.userStatus(),
  avatar: Random.avatar(),
  dateOfBirth: LocalizedDate.epochMinusMonths(240),
  pointOfContactName: Random.fullName(),
  isPayable: Random.boolean
}));

export function aUser(override = {}): User {
  return factory.build(override) as User;
}
