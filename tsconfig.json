{"compilerOptions": {"baseUrl": ".", "target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "paths": {"@src/*": ["src/*"]}, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "strict": true, "skipLibCheck": true, "outDir": "dist", "noEmit": true, "allowImportingTsExtensions": true}, "include": ["images.d.ts", "src/**/*.ts", "src/**/*.tsx", "jest.setup.ts"], "exclude": ["node_modules"]}