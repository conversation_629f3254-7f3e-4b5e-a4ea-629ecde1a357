.onboarding-creator.mg-container {
  @apply pb-meas0;
}
.onboarding-creator section {
  @apply w-full;
}
.onboarding-creator section > form {
  @apply w-full pt-meas32;
}
.creator-types-container-creator-type-container .card-container {
  @apply border-t border-white border-opacity-[0.33] pt-meas32;
}
@media screen and (min-width: 768px) {
  .onboarding-creator #stepper-content {
    overflow: auto;
  }
  .onboarding-creator .slider-content {
    transform: none !important; /*Unfortunately had to do this to override inline style*/
  }
}
.onboarding-creator .mg-header-container {
  @apply flex-1 px-meas8;
}
.onboarding-creator .mg-header-logo a {
  @apply flex items-center;
}
.onboarding-creator .mg-header-close {
  @apply mr-meas7 mt-meas0;
}
.onboarding-creator .mg-header {
  @apply items-center;
}
.onboarding-creator {
  @apply px-meas0 pb-meas0;
}
.onboarding-creator .mg-page-onboarding-creator form {
  @apply w-[100%];
}
