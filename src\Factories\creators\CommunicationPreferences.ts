import { Factory } from "fishery";
import Random from "../Random";
import { CommunicationPreferencesResponse } from "../../shared/creators/CommunicationPreferences";
import { aPreferredLanguage } from "../languages/PreferredLanguage";
const factory = Factory.define<CommunicationPreferencesResponse>(() => ({
  email: Random.email(),
  phone: Random.phone(),
  discord: Random.userName(),
  preferredLanguage: aPreferredLanguage(),
  contentLanguages: [aPreferredLanguage()]
}));

export function aCommunicationPreferences(override = {}): CommunicationPreferencesResponse {
  return factory.build(override);
}
