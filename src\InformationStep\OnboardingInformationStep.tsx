import { NextRouter } from "next/router";
import React, { FC, ReactElement, ReactNode, useCallback, useEffect, useRef, useState } from "react";
import { FieldValues } from "react-hook-form";
import { CreatorFormRules } from "../CreatorCode/CreatorCodeForm";
import { CommunicationFormRules } from "../utils/CreatorForm";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  VALIDATION_ERROR
} from "../utils";
import {
  BrowserAnalytics,
  CloseHandler,
  ContentUrl,
  Dispatch,
  ErrorHandling,
  FutureCreator,
  Layout,
  OnboardingStepConfiguration,
  PlatformOption,
  PreferredLanguage,
  PreferredPlatform,
  State,
  ValidationError
} from "../types";
import { Country, Language } from "@eait-playerexp-cn/metadata-types";
import CancelRegistrationModal from "../common/CancelRegistrationModal/CancelRegistrationModal";
import InformationInputs from "./InformationInput/InformationInput";
import Form from "../utils/Form";
import Footer from "../common/Footer/Footer";
import CreatorWithProgramCode, { CreatorWithProgramCodeResponse } from "../shared/creators/CreatorWithProgramCode";
import CreatorService, { CreatorWithpreferredPronouns, CreatorWithProgamCodeProfile } from "../Browser/CreatorService";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Option, Toast } from "@eait-playerexp-cn/core-ui-kit";
import { NavStep } from "../CreatorTypeStep/CreatorTypePage/CreatorTypePage";
import AccountInformation from "../shared/creators/AccountInformation";
import { MailingAddressPayload, RegisterCreatorRequest, UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import MailingAddress from "../shared/creators/MailingAddress";
import SubmittedContentService from "../Browser/SubmittedContentService";

export const PreferredPronounOtherOption = "Other (Please specify)";

export type Information = {
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  defaultGamerTag: string;
  originEmail: string;
  contentLanguages?: Array<Language>;
  preferredLanguage?: PreferredLanguage;
  country?: Country;
  dateOfBirth?: string;
  countryCode?: string;
};

export type InformationFormData = InformationFormProps & MailingAddressPayload;

export type PageLabels = {
  interestedUserDescription2: string;
  interestedUserDescription1: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  creatorTitle: string;
  yes: string;
  no: string;
  userDescription1: string;
  userDescription2: string;
  primaryPlatform: string;
  save: string;
};

export type ConnectAccountLabels = {
  title: string;
  message: string;
  subTitle: string;
  myAccount: string;
  addAccount: string;
  description: string;
  accounts: {
    youTube: string;
    facebook: string;
    twitch: string;
    instagram: string;
    tiktok: string;
  };
  modalTitle: string;
  modalMessage: string;
  modalConfirmationTitle: string;
  modalConfirmationTitleFB: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  messages: {
    removeAccountTitle: string;
    removeAccountDescription: string;
    cannotConnectInstaAccount: string;
    cannotConnectInstaAccountHeader: string;
    actionTitle: string;
    actionDescription1: string;
    actionDescription2: string;
    actionDescription3: string;
    actionDescription4: string;
    youtubeNoChannelError: string;
  };
  modal: {
    removeAccountTitle: string;
    removeAccountDescription1: string;
    removeAccountDescription2: string;
  };
  buttons: {
    cancel: string;
    remove: string;
  };
  subscribers: string;
  removeAccount: string;
  expireAccount: string;
  or: string;
  reconnectAccount: string;
  connectNewAccount: string;
  connectNewAccountDescription: string;
  connectNewAccountDescriptionWithTikTok: string;
  myProfile: string;
  information: string;
  gamePreferences: string;
  creatorType: string;
  connectedAccounts: string;
  communicationSettings: string;
  legalDocuments: string;
  paymentInformation: string;
  pointOfContact: string;
  discord: string;
  email: string;
  verificationPending: string;
  reVerifyAccount: string;
  cancel: string;
  close: string;
  connect: string;
  remove: string;
};

export type FormLabels = {
  infoTitle: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  preferredEmail: string;
  eaEmailID: string;
  EAID: string;
  country: string;
  street: string;
  state: string;
  city: string;
  zipCode: string;
  contentMediaTitle: string;
  contentMediaDescription: string;
  contentUrlPlaceholder: string;
  contentUrl: string;
  contentFollowers: string;
  contentFollowersPlaceholder: string;
  basicInformation: string;
  addAnother: string;
  contentLanguagesTitle: string;
  contentLanguagesDescription: string;
  contentLanguage: string;
  languageTitle: string;
  languageDescription: string;
  language: string;
  cancel: string;
  next: string;
  duplicateUrl: string;
  urlScanFailed: string;
  followersMaxLength: string;
  selectCountry: string;
  remove: string;
  ok: string;
  calendar: string;
  close: string;
  connectSocialMediaAccountTitle: string;
  connectSocialMediaAccountDescription: string;
  additionalContentAndWebsiteTitle: string;
  additionalContentAndWebsiteDescription: string;
  websiteUrlLabel: string;
  additionalLinkPlaceholder: string;
  addMoreUrlLabel: string;
  invalidUrl: string;
  ageMustBe18OrOlder: string;
  preferredName?: string;
  preferredPronouns?: string;
  preferredPronoun?: string;
  enterPronoun?: string;
  selectProunoun?: string;
  platformPreferences: string;
  platformPreferencesTitle: string;
  primaryPlatform: string;
  secondaryPlatforms: string;
  secondaryPlatformsTitle: string;
  profilePicture: {
    termsAndConditionsLast: string;
    avatarRequired: string;
    message: string;
    termsAndConditionsFirst: string;
    title: string;
    avatarInvalid: string;
    avatarMoreThanLimit: string;
    termsAndConditionsMiddle: string;
  };
  browse: string;
};

export type InformationPageLabels = {
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  interestedCreatorTitle: string;
  yes: string;
  no: string;
  interestedUserDescription1: string;
  interestedUserDescription2: string;
};

export type Labels = {
  infoLabels: {
    interestedCreatorTitle: string;
    messages: {
      firstNameTooLong: string;
      lastNameTooLong: string;
      street: string;
      streetTooLong: string;
      city: string;
      cityTooLong: string;
      state: string;
      stateTooLong: string;
      zipCode: string;
      zipCodeTooLong: string;
      tShirtSize: string;
      entityType: string;
      businessName: string;
      businessNameTooLong: string;
      email: string;
      emailTooLong: string;
      emailInvalid: string;
      url: string;
      invalidUrl: string;
      followersMaxLength: string;
      firstName: string;
      lastName: string;
      dateOfBirth: string;
      dateOfBirthInvalid: string;
      ageMustBe18OrOlder: string;
      country: string;
      preferredNameTooLong?: string;
      preferredPronoun?: string;
      primaryPlatform: string;
    };
  };
  translation: {
    messages: {
      preferredEmailTooLong: string;
      preferredEmailInvalid: string;
      preferredPhoneNumber: string;
      preferredPhoneNumberTooLong: string;
      contentLanguage: string;
      language: string;
      preferredEmail: string;
    };
  };
  formLabels: FormLabels & { labels: FormLabels };
  pageLabels: PageLabels;
  layout: Layout;
};

export type InformationFormProps = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName: string;
  lastName: string;
  originEmail: string;
  dateOfBirth: number;
  needsMigration: boolean;
  status: string;
  registrationDate: number;
  isPayable: boolean;
  creatorCode: string;
  preferredName?: string;
  preferredPronoun?: string;
  preferredPronouns?: { value: string; label: string };
  primaryPlatform?: Option;
  secondaryPlatforms?: Option[];
  contentUrls?: ContentUrl[];
};

export type ConnectAccounts = Array<{ value: string; imageUrl: string; redirectUrl: string }>;

export type InformationStepConfiguration = OnboardingStepConfiguration & {
  formFields: {
    preferredName?: { required: boolean };
    preferredPronouns?: { required: boolean };
    preferredPronoun?: { required: boolean };
    firstName: { required: boolean };
    lastName: { required: boolean };
    dateOfBirth: { required: boolean };
    country: { required: boolean };
    street: { required: boolean };
    city: { required: boolean };
    state: { required: boolean };
    zipCode: { required: boolean };
    defaultGamerTag?: { required: boolean };
    originEmail?: { required: boolean };
    preferredPlatforms?: { required: boolean };
    socialLinks?: { required: boolean };
    profilePicture?: { required: boolean };
  };
  navigateToNextPage: string;
};

export type OnboardingInformationStepProps = {
  analytics: BrowserAnalytics;
  stableDispatch: Dispatch;
  state: State;
  labels: Labels;
  PROGRAM_CODE: string;
  errorHandling: ErrorHandling;
  configuration: InformationStepConfiguration;
  router: NextRouter;
  locale: string;
  showConfirmation: boolean;
  setShowConfirmation: (show: boolean) => void;
  onClose: () => void;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  FLAG_COUNTRIES_BY_TYPE: boolean;
  isExistingCreator: boolean;
  futureCreator: Record<string, unknown>;
  user: Record<string, unknown>;
  preferredPronounsOptions?: { value: string; label: string }[];
  registrationCode: string;
};

export type Rules = Partial<CreatorFormRules & CommunicationFormRules>;

export type CreatorProps = CreatorWithProgamCodeProfile & CreatorWithProgramCode;

export const interestedCreatorPages = {
  information: "Information",
  creatorTypes: "CreatorTypes",
  franchises: "FranchisesYouPlay"
};
const OnboardingInformationStep: FC<OnboardingInformationStepProps> = ({
  analytics,
  stableDispatch,
  state,
  labels,
  errorHandling,
  configuration,
  onClose,
  showConfirmation,
  setShowConfirmation,
  router,
  FLAG_COUNTRIES_BY_TYPE,
  errorToast,
  PROGRAM_CODE,
  futureCreator,
  user,
  preferredPronounsOptions,
  registrationCode
}) => {
  const { formLabels, pageLabels, layout } = labels;

  const {
    main: { unhandledError }
  } = layout;

  const { isError = false, isValidationError = false, onboardingSteps = [] } = state;
  const [creator, setCreator] = useState<CreatorProps | null>(null);
  const [countries, setCountries] = useState<Country[]>([]);
  const [platforms, setPlatforms] = useState<PlatformOption[]>([]);

  const creatorService = configuration.creatorsClient
    ? new CreatorsService(configuration.creatorsClient, configuration.DEFAULT_AVATAR_IMAGE)
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? new CreatorService(configuration.onBoardingClient)
    : undefined;
  const metadataService = new MetadataService(configuration.metadataClient);
  const submittedContentService = configuration.onBoardingClient
    ? new SubmittedContentService(configuration.onBoardingClient)
    : undefined;

  // Ref to access validation function from AdditionalContentAndWebsiteLinks component
  const additionalLinksRef = useRef<{
    validateAdditionalLinks: (contentUrls: ContentUrl[]) => Promise<string[] | []>;
    hasValidationErrors: () => boolean;
  }>(null);

  const {
    modalConfirmationTitle,
    confirmationDesc1,
    confirmationDesc2,
    interestedUserDescription1,
    interestedUserDescription2
  } = pageLabels;

  const handleCancelRegistration = useCallback(() => {
    if (analytics.canceledOnboardingFlow) {
      analytics.canceledOnboardingFlow({ locale: router.locale as string, page: router.pathname });
    }
    router.push("/api/logout");
  }, [router]);

  const defaultPronounsOptions = preferredPronounsOptions
    ? preferredPronounsOptions.map((pronoun) => ({ value: pronoun.value, label: pronoun.label }))
    : [];
  const preferredPronouns = [{ label: labels.formLabels.selectProunoun || "", value: "" }, ...defaultPronounsOptions];

  const handleModalClose = useCallback(() => setShowConfirmation(false), []);

  const submitHandle = useCallback(
    async (formData: FieldValues) => {
      const data = formData as InformationFormData;
      try {
        const currentStep = (onboardingSteps as Array<NavStep>).find((step) => step.href === router.pathname);
        data.dateOfBirth = LocalizedDate.fromFormattedDate(data.dateOfBirth as unknown as string).format(
          "YYYY-MM-DD"
        ) as unknown as number;
        data.nucleusId = creator?.accountInformation.nucleusId;
        data.originEmail = creator?.accountInformation.originEmail;
        const preferredPlatforms: PreferredPlatform[] = [];
        if (configuration.formFields.preferredPlatforms && data.primaryPlatform && data.secondaryPlatforms) {
          const primaryPlatform: PreferredPlatform = {
            id: data.primaryPlatform.value,
            type: "PRIMARY"
          };
          const secondaryPlatforms: PreferredPlatform[] = data.secondaryPlatforms
            .filter((platform) => platform.value)
            .map((platform) => ({
              id: platform.value,
              type: "SECONDARY"
            }));

          preferredPlatforms.push(primaryPlatform, ...secondaryPlatforms);
        }
        let validContentUrls: string[] | [] = [];
        if (configuration.formFields.socialLinks && data?.contentUrls && additionalLinksRef.current) {
          const additionalWebsiteUrls = await additionalLinksRef.current.validateAdditionalLinks(data.contentUrls);
          // Check if there are any validation errors
          console.log({hasValidationErrors: additionalLinksRef.current.hasValidationErrors()})
          if (additionalLinksRef.current.hasValidationErrors()) {
            // Don't proceed with form submission if there are validation errors
            // The errors are already displayed via the component's state
            return;
          }
          if (additionalWebsiteUrls?.length > 0) {
            validContentUrls = additionalWebsiteUrls;
          }
        }
        let accountInformation = {
          firstName: data.firstName,
          lastName: data.lastName,
          dateOfBirth: data.dateOfBirth,
          nucleusId: data.nucleusId,
          originEmail: data.originEmail,
          defaultGamerTag: data?.defaultGamerTag || user?.username || user?.defaultGamerTag || futureCreator?.username,
          status: creator?.id === null ? "UNREGISTERED" : data.status || null,
          isPayable: data.isPayable,
          creatorCode: data.creatorCode,
          preferredName: "",
          preferredPronouns: ""
        };
        let registerInfo = { ...data, creatorConnectedProgram: PROGRAM_CODE };
        if (configuration?.formFields?.preferredName || configuration?.formFields?.preferredPronouns) {
          registerInfo = {
            ...registerInfo,
            preferredName: data.preferredName,
            preferredPronouns:
              data.preferredPronouns?.value === PreferredPronounOtherOption
                ? data.preferredPronoun
                : data.preferredPronouns?.value
          } as any; // eslint-disable-line @typescript-eslint/no-explicit-any
          accountInformation = {
            ...accountInformation,
            preferredName: data.preferredName as string,
            preferredPronouns:
              data.preferredPronouns?.value === PreferredPronounOtherOption
                ? (data.preferredPronoun as string)
                : (data.preferredPronouns?.value as string)
          };
        }
        const mailingAddress = {
          country: { value: (data.country as unknown as Country).value, label: data?.country?.name },
          street: data.street,
          city: data.city,
          state: data.state,
          zipCode: data.zipCode
        };
        if (creator?.id) {
          if (legacyCreatorService) {
            await legacyCreatorService.update({
              accountInformation: accountInformation as AccountInformation,
              mailingAddress: mailingAddress as MailingAddress,
              creatorConnectedProgram: PROGRAM_CODE
            });
          } else if (creatorService) {
            const bodyValues = {
              accountInformation: {
                firstName: accountInformation.firstName,
                lastName: accountInformation.lastName,
                dateOfBirth: accountInformation.dateOfBirth,
                preferredName: accountInformation?.preferredName || "",
                preferredPronouns: accountInformation?.preferredPronouns || null,
                originEmail: accountInformation.originEmail,
                defaultGamerTag: accountInformation.defaultGamerTag
              },
              mailingAddress: {
                street: data.street,
                city: data.city,
                state: data.state,
                zipCode: data.zipCode,
                country: {
                  code: (data.country as unknown as Country).value,
                  name: data.country.name
                }
              },
              ...(configuration.formFields.preferredPlatforms &&
                preferredPlatforms.length > 0 && { preferredPlatforms }),
              ...(configuration.formFields.socialLinks &&
                validContentUrls.length > 0 && { socialLinks: validContentUrls }),
              program: { code: PROGRAM_CODE },
              ...(registrationCode && { registrationCode: registrationCode })
            };
            await creatorService.updateCreator(bodyValues as unknown as UpdateCreatorRequest);
          }
        } else {
          if (legacyCreatorService) {
            await legacyCreatorService.register({
              information: registerInfo
            } as unknown as CreatorWithProgramCode);
          } else if (creatorService) {
            const bodyValues = {
              accountInformation: {
                firstName: accountInformation.firstName,
                lastName: accountInformation.lastName,
                dateOfBirth: accountInformation.dateOfBirth,
                preferredName: accountInformation?.preferredName || "",
                preferredPronouns: accountInformation?.preferredPronouns || null,
                originEmail: accountInformation.originEmail,
                defaultGamerTag: accountInformation.defaultGamerTag,
                nucleusId: accountInformation.nucleusId
              },
              mailingAddress: {
                street: data.street,
                city: data.city,
                state: data.state,
                zipCode: data.zipCode,
                country: {
                  code: (data.country as unknown as Country).value,
                  name: data.country.name
                }
              },
              ...(configuration.formFields.preferredPlatforms &&
                preferredPlatforms.length > 0 && { preferredPlatforms }),
              ...(configuration.formFields.socialLinks &&
                validContentUrls.length > 0 && { socialLinks: validContentUrls }),
              program: PROGRAM_CODE,
              ...(registrationCode && { registrationCode: registrationCode })
            };
            await creatorService.registerCreator(bodyValues as unknown as RegisterCreatorRequest);
          }
        }
        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        router.push(configuration.navigateToNextPage);
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
      }
    },
    [stableDispatch, creator, router, onboardingSteps, configuration.formFields]
  );

  const { pending, execute: submitInformation } = useAsync(submitHandle, false);

  useEffect(() => {
    async function fetchData() {
      try {
        stableDispatch({ type: LOADING, data: true });
        if (!futureCreator) {
          const creator: any = legacyCreatorService // eslint-disable-line @typescript-eslint/no-explicit-any
            ? (await legacyCreatorService.getCreatorWithProgramCode(preferredPronouns, PreferredPronounOtherOption))
                .data
            : await creatorService?.getCreator(PROGRAM_CODE);
          if (configuration.formFields.preferredPlatforms) {
            if (creator.preferredPrimaryPlatform) {
              creator.preferredPrimaryPlatforms = {
                value: creator.preferredPrimaryPlatform.id,
                label: creator.preferredPrimaryPlatform.name
              };
            }
            creator.preferredSecondaryPlatforms =
              creator.preferredSecondaryPlatforms?.map((platform: { id: string; name: string }) => {
                return {
                  ...platform,
                  value: platform.id,
                  label: platform.name
                };
              }) || [];
          }
          if (!legacyCreatorService) {
            const preferredPronounData = new CreatorWithpreferredPronouns(
              creator,
              preferredPronouns,
              PreferredPronounOtherOption
            );
            creator.preferredPronouns = preferredPronounData.preferredPronouns;
            creator.preferredPronoun = preferredPronounData.preferredPronoun;
          }
          setCreator(creator);
        } else {
          setCreator(
            new CreatorWithProgamCodeProfile({
              accountInformation: {
                originEmail: futureCreator.email,
                dateOfBirth: futureCreator.dateOfBirth,
                nucleusId: futureCreator.nucleusId,
                defaultGamerTag: user.username || futureCreator.username
              }
            } as CreatorWithProgramCodeResponse) as unknown as CreatorProps
          );
        }

        const countriesAction = FLAG_COUNTRIES_BY_TYPE
          ? metadataService.getCountriesMatching()
          : metadataService.getCountries();
        const countriesData = await countriesAction;
        if (countriesData) {
          setCountries(countriesData);
        }
        if (configuration.formFields.preferredPlatforms) {
          const platforms = await metadataService.getPlatformsMatching({ type: "SITE" });
          if (platforms) setPlatforms(platforms);
        }
        if (analytics.startedOnboardingFlow) {
          analytics.startedOnboardingFlow({ locale: router.locale as string });
        }
        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
        stableDispatch({ type: LOADING, data: false });
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError || toastContent(isValidationError as unknown as ValidationError[])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  const modalLabels = {
    title: modalConfirmationTitle,
    yes: pageLabels.yes,
    no: pageLabels.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <div className="onboarding-creator onboarding-information-page">
      <div className="mg-intro">
        <h3 className="onboarding-intro-title">{formLabels.basicInformation}</h3>
        <div className="onboarding-intro-description">
          {interestedUserDescription1}{" "}
          <strong className="gamer-tag">{creator?.accountInformation.defaultGamerTag}!</strong>{" "}
          {interestedUserDescription2}
        </div>
      </div>
      {creator && countries && (
        <Form mode="onChange" onSubmit={submitInformation}>
          <InformationInputs
            {...{
              labels,
              creator,
              countries,
              user,
              stableDispatch,
              configuration,
              preferredPronouns,
              platforms,
              futureCreator: futureCreator as FutureCreator,
              submittedContentService,
              additionalLinksRef
            }}
          />
          <Footer
            {...{
              buttons: layout.buttons,
              onCancel: onClose,
              disableSubmit: pending,
              isPending: pending
            }}
          />
        </Form>
      )}
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: modalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </div>
  );
};

export default OnboardingInformationStep;
