import { PointOfContactResponse } from "@eait-playerexp-cn/creator-types";
import HardwarePartner, { HardwarePartnerResponse } from "../HardwarePartner";
import PointOfContact from "./PointOfContact";

export type AdditionalInformationResponse = {
  hardwarePartners: Array<HardwarePartnerResponse>;
  hoodieSize?: string;
  pointOfContact?: PointOfContactResponse;
  tier?: string;
};

export default class AdditionalInformation {
  static fromApi(data: AdditionalInformationResponse): AdditionalInformation {
    return new AdditionalInformation(
      data.hardwarePartners ? data.hardwarePartners.map((item) => HardwarePartner.fromApi(item)) : [],
      data.hoodieSize,
      data.pointOfContact ? PointOfContact.fromApi(data.pointOfContact) : undefined,
      data.tier ?? undefined
    );
  }

  constructor(
    readonly hardwarePartners: Array<HardwarePartner> | [],
    readonly hoodieSize?: string,
    readonly pointOfContact?: PointOfContact,
    readonly tier?: string
  ) {}
}
