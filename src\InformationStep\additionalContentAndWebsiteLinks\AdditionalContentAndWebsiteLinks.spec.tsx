import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import React, { ReactElement } from "react";
import AdditionalContentAndWebsiteLinks from "./AdditionalContentAndWebsiteLinks";
import { InformationLabels } from "../../Translations/Information";

describe("AdditionalContentAndWebsiteLinks", () => {
  const onBoardingFormLabels = {
    ...InformationLabels,
    labels: {
      additionalContentAndWebsiteTitle: InformationLabels.formLabels.additionalContentAndWebsiteTitle,
      additionalContentAndWebsiteLinks: InformationLabels.formLabels.additionalContentAndWebsiteDescription,
      additionalLinkPlaceholder: InformationLabels.formLabels.additionalLinkPlaceholder,
      websiteUrlLabel: InformationLabels.formLabels.websiteUrlLabel
    },
    messages: {
      duplicateUrl: InformationLabels.formLabels.duplicateUrl
    },
    addMoreUrlLabel: InformationLabels.formLabels.addMoreUrlLabel,
    remove: InformationLabels.formLabels.remove
  };

  const additionalContentAndWebsiteLinksProps = {
    labels: onBoardingFormLabels,
    socialLinks: []
  };

  // Wrapper component to provide form context
  const FormWrapper = ({ children }: { children: ReactElement }) => {
    const methods = useForm({ defaultValues: { contentUrls: [{ url: "", followers: "" }] } });
    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  it("shows the content with default props", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );
    const contentTitle = screen.getByRole("heading", {
      level: 4,
      name: onBoardingFormLabels.labels.additionalContentAndWebsiteTitle
    });

    expect(contentTitle).toBeInTheDocument();
    expect(contentTitle).toHaveClass("onboarding-mg-information-additional-content-title");
    expect(screen.getByText(onBoardingFormLabels.labels.additionalContentAndWebsiteLinks)).toBeInTheDocument();
    expect(screen.getByText(onBoardingFormLabels.labels.additionalContentAndWebsiteLinks)).toHaveClass(
      "onboarding-mg-information-additional-content-description"
    );

    expect(screen.getByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel })).toBeInTheDocument();
    expect(screen.getByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel })).toHaveAttribute(
      "placeholder",
      onBoardingFormLabels.labels.additionalLinkPlaceholder
    );
    expect(screen.getByRole("list", { name: "onboarding-mg-information-additional-content-urls" })).toBeInTheDocument();
    expect(screen.getByRole("list", { name: "onboarding-mg-information-additional-content-urls" })).toHaveClass(
      "onboarding-mg-information-additional-content-urls"
    );
    expect(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel })).toBeInTheDocument();
    expect(screen.getByTestId("onboarding-mg-content-url-add-more-icon")).toBeInTheDocument();
    expect(screen.getByTestId("onboarding-mg-content-url-add-more-icon")).toHaveClass(
      "onboarding-mg-content-url-add-more-icon"
    );
    expect(screen.getByText(onBoardingFormLabels.addMoreUrlLabel)).toBeInTheDocument();
    expect(screen.getByText(onBoardingFormLabels.addMoreUrlLabel)).toHaveClass(
      "onboarding-mg-content-url-add-more-text"
    );
  });

  it("shows second input box with delete icon when Add button is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));

    expect(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel }).length).toBe(2);
    expect(screen.getByTestId("onboarding-mg-content-url-icon")).toBeInTheDocument();
  });

  it("removes input box when delete icon is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));
    expect(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel }).length).toBe(2);
    expect(screen.getByTestId("onboarding-mg-content-url-icon")).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.remove }));

    expect(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel }).length).toBe(1);
    expect(screen.queryByTestId("onboarding-mg-content-url-icon")).not.toBeInTheDocument();
  });

  it("clears input when clear icon is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );
    const url = "https://www.example.com";
    await userEvent.type(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel })[0], url);
    expect(screen.getByLabelText(onBoardingFormLabels.labels.websiteUrlLabel)).toHaveValue(url);

    await userEvent.click(screen.getByRole("button", { name: `Clear ${onBoardingFormLabels.labels.websiteUrlLabel}` }));

    expect(screen.getByLabelText(onBoardingFormLabels.labels.websiteUrlLabel)).toHaveValue("https://");
  });

  it("shows error label when duplicate url is entered", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );
    const url = "https://www.example.com";
    await userEvent.type(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel })[0], url);
    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));

    await userEvent.type(screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel })[1], url);

    expect(screen.getByText(onBoardingFormLabels.messages.duplicateUrl)).toBeInTheDocument();
  });

  it("clears the error when duplicate url is modified", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );
    const url1 = "https://www.example.com";
    const url2 = "https://www.example2.com";
    await userEvent.type(screen.getAllByText(onBoardingFormLabels.labels.websiteUrlLabel)[0], url1);
    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));
    expect(screen.getAllByText(onBoardingFormLabels.labels.websiteUrlLabel).length).toBe(2);
    await userEvent.type(screen.getAllByText(onBoardingFormLabels.labels.websiteUrlLabel)[1], url1);
    expect(screen.getByText(onBoardingFormLabels.messages.duplicateUrl)).toBeInTheDocument();

    await userEvent.type(screen.getAllByText(onBoardingFormLabels.labels.websiteUrlLabel)[1], url2);

    expect(screen.queryByText(onBoardingFormLabels.messages.duplicateUrl)).not.toBeInTheDocument();
  });

  it("initializes with social links when provided", async () => {
    const socialLinks = ["https://twitter.com/example", "https://youtube.com/example"];
    const propsWithSocialLinks = {
      ...additionalContentAndWebsiteLinksProps,
      socialLinks
    };
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...propsWithSocialLinks} />
      </FormWrapper>
    );

    const textboxes = screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel });
    expect(textboxes).toHaveLength(2);
    expect(textboxes[0]).toHaveValue(socialLinks[0]);
    expect(textboxes[1]).toHaveValue(socialLinks[1]);
  });

  it("initializes with empty field when no social links and no existing fields", async () => {
    const FormWrapperWithNoFields = ({ children }: { children: ReactElement }) => {
      const methods = useForm({ defaultValues: { contentUrls: [] } });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };
    const propsWithEmptySocialLinks = {
      ...additionalContentAndWebsiteLinksProps,
      socialLinks: []
    };
    render(
      <FormWrapperWithNoFields>
        <AdditionalContentAndWebsiteLinks {...propsWithEmptySocialLinks} />
      </FormWrapperWithNoFields>
    );

    const textboxes = screen.getAllByRole("textbox", { name: onBoardingFormLabels.labels.websiteUrlLabel });
    expect(textboxes).toHaveLength(1);
    expect(textboxes[0]).toHaveValue("");
  });

  // Note: Validation error tests are now handled internally by the component
  // when the validation function is called, so they don't need external props

  it("hides add button when maximum URLs (10) are reached", async () => {
    const FormWrapperWithManyUrls = ({ children }: { children: ReactElement }) => {
      const methods = useForm({
        defaultValues: {
          contentUrls: Array(10)
            .fill(null)
            .map(() => ({ url: "", followers: "" }))
        }
      });
      return <FormProvider {...methods}>{children}</FormProvider>;
    };
    render(
      <FormWrapperWithManyUrls>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapperWithManyUrls>
    );

    expect(screen.queryByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel })).not.toBeInTheDocument();
  });

  it("shows delete button for all URLs except the first one", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));
    await userEvent.click(screen.getByRole("button", { name: onBoardingFormLabels.addMoreUrlLabel }));

    const deleteButtons = screen.getAllByRole("button", { name: onBoardingFormLabels.remove });
    expect(deleteButtons).toHaveLength(2); // Only second and third URLs should have delete buttons
  });

  it("is accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks {...additionalContentAndWebsiteLinksProps} />
      </FormWrapper>
    );

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
