import { Factory } from "fishery";
import Random from "../Random";
import { PreferredLanguageResponse } from "src/shared/languages/PreferredLanguage";

const factory = Factory.define<PreferredLanguageResponse>(() => ({
  code: Random.locale(),
  name: Random.locale(),
  id: Random.uuid()
}));

export function aPreferredLanguage(override = {}): PreferredLanguageResponse {
  return factory.build(override);
}
