import React, { memo, useMemo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import CheckboxCards from "src/common/cards/CheckboxCards/CheckboxCards";
import { CreatorType, FormLabels } from "../creator-type";
import { CreatorTypeFormLabels } from "../CreatorTypeForm/CreatorTypeForm";
import Footer, { FooterProps } from "src/common/Footer/Footer";

export type CreatorTypeInputsProps = {
  creatorsType: CreatorType[];
  values: string[];
  readOnly?: boolean;
  formLabels: FormLabels & CreatorTypeFormLabels;
  footerProps: FooterProps;
  basePath?: string;
};

export type CreatorsTypeLabels = {
  label: string;
  value: string;
};
export default memo(function CreatorTypeInputs({
  creatorsType,
  values = [],
  formLabels,
  readOnly,
  footerProps,
  basePath
}: CreatorTypeInputsProps) {
  const methods = useFormContext();
  const { control } = methods;
  const { requiredMessage, creatorsTypeLabels } = formLabels;
  const creatorTypesProps = useMemo(
    () => ({
      name: "creatorTypes",
      requiredMessage: requiredMessage,
      values: creatorsType.filter((item) => values.indexOf(item.value) !== -1),
      items: creatorsType.map((item) => {
        const label =
          creatorsTypeLabels.find((creatorTypelabel) => creatorTypelabel.value === item.value)?.label ?? item.label;
        return {
          ...item,
          label: label,
          checked: values.indexOf(item.value) !== -1
        };
      })
    }),
    [creatorsType, values]
  );

  return (
    <>
      {creatorTypesProps && (
        <div className="creator-type-card-container">
          {readOnly ? (
            <CheckboxCards items={creatorTypesProps.items} readOnly={true} basePath={basePath} />
          ) : (
            <Controller
              control={control}
              name={creatorTypesProps.name}
              defaultValue={creatorTypesProps.values}
              rules={{ required: creatorTypesProps.requiredMessage }}
              render={({ field, fieldState: { error } }) => (
                <CheckboxCards
                  {...field}
                  errorMessage={error ? error.message : ""}
                  basePath={basePath}
                  items={creatorTypesProps.items}
                  disabled={false}
                />
              )}
            />
          )}
          <Footer {...footerProps} />
        </div>
      )}
    </>
  );
});
