import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorService from "./CreatorService";

describe("CreatorService", () => {
  it("updates profile picture", async () => {
    const formData = new FormData();
    const client = { upload: jest.fn() } as unknown as TraceableHttpClient;
    const service = new CreatorService(client);

    await service.updateProfilePicture(formData);

    expect(client.upload).toHaveBeenCalledTimes(1);
    expect(client.upload).toHaveBeenCalledWith("/api/avatar", { body: formData });
  });
});
