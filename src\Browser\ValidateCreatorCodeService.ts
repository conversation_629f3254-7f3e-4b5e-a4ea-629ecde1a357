import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type ValidateCreatorCodeResponse = {
  creatorCode: string;
  isValidCode: boolean;
};

export type ContentCreatorCodeBody = {
  contents: Array<string>;
};

export type ScannedCreatorCode = {
  content: string;
  healthy: boolean;
};

export type ContentScanResult = {
  results: Array<ScannedCreatorCode>;
};

export class ValidateCreatorCodeService {
  constructor(private readonly client: TraceableHttpClient) {}

  async validateCreatorCode(creatorCode: string): Promise<ValidateCreatorCodeResponse> {
    const response = await this.client.get(`/api/v2/creator-codes/${creatorCode}`);
    return response.data;
  }

  async verifyCreatorCodeContent(creatorCodeBody: ContentCreatorCodeBody): Promise<ContentScanResult> {
    const response = await this.client.post("/api/verify-creator-code", { body: creatorCodeBody });
    return response.data;
  }
}

export default ValidateCreatorCodeService;
