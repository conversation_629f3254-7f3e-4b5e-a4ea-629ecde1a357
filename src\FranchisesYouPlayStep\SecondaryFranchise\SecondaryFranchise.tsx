import React from "react";
import { Control, Controller } from "react-hook-form";
import CheckboxCards from "src/common/cards/CheckboxCards/CheckboxCards";

type FranchisesOptions = {
  value: string;
  label: string;
  image: string;
  checked: boolean;
};

export type FranchiseItem = {
  value: string;
  label: string;
  image: string;
};

type SecondaryFranchiseInputsProps = {
  name: string;
  items: FranchisesOptions[];
  values: FranchiseItem[];
  control: Control;
  disabled: boolean;
  secondaryFranchisefallbackImage: string;
  basePath?: string;
};

const SecondaryFranchise = ({
  name,
  items = [],
  values = [],
  control,
  disabled,
  secondaryFranchisefallbackImage,
  basePath
}: SecondaryFranchiseInputsProps): JSX.Element => {
  return (
    <>
      {disabled ? (
        <img
          alt="Franchise"
          className={"secondary-franchise-image-unselected"}
          src={secondaryFranchisefallbackImage ?? `${basePath}/img/franchises-you-play/sec-franchise-unselected.png`}
        />
      ) : (
        <Controller
          control={control}
          name={name}
          defaultValue={values}
          render={({ field }) => <CheckboxCards {...field} items={items} disabled={disabled} basePath={basePath} />}
        />
      )}
    </>
  );
};

export default SecondaryFranchise;
