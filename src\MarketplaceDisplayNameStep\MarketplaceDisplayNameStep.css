.marketplace-display-name-form-container {
  @apply h-full w-full;
}
.marketplace-display-name-form-title {
  @apply font-display-bold font-bold text-white xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}
.marketplace-display-name-form-description {
  @apply mt-meas8 font-text-regular text-[16px] font-normal leading-6 tracking-[0.8px] text-white md:mt-meas10;
}
.marketplace-display-name-form-tips {
  @apply mt-meas16;
}
.marketplace-display-name-form-tips > div {
  @apply mt-meas4 font-display-bold text-[20px] font-bold leading-7 tracking-[0px];
}
ul.marketplace-display-name-list-container li.marketplace-display-name-list {
  @apply flex;
}
ul.marketplace-display-name-list-container li.marketplace-display-name-list::before {
  @apply m-[10px] inline-block h-meas2 min-w-meas2 rounded-full bg-[#FFFFFF] content-[''];
}
.marketplace-display-name-form-validation-info-container {
  @apply mt-meas16 flex items-start;
}
.marketplace-display-name-form-validation-info-container > img {
  @apply mt-[2px];
}
.marketplace-display-name-form-validation-info-label {
  @apply ml-meas4 text-desktop-body-small font-bold text-white;
}
.marketplace-display-name-form-input-container {
  @apply mt-[1.875rem] md:mt-meas16 mb-meas16;
}
.marketplace-display-name-form-checkbox-container {
  @apply mt-[1.875rem] md:mt-meas16 mb-meas16;
}
.marketplace-display-name-form-footer-container {
  @apply sticky mt-[1.875rem] w-full border-t-0 border-white border-opacity-[0.33] text-right md:mt-meas22;
}
.marketplace-display-name-form-footer-container svg.icon {
  @apply ml-meas4;
}
.marketplace-display-name-form-footer-container .btn {
  @apply bg-[#872AE6] px-meas8 py-meas6;
}
.marketplace-display-name-form-footer-container .btn:first-child {
  @apply mr-[10px] text-white;
  background-color: transparent;
}
.marketplace-display-name-form-footer-container .btn-primary:disabled {
  @apply border-error-60 bg-[#872AE6];
}

.marketplace-display-name-form-information {
  @apply w-full border-b-2 border-[#A5A5A5] border-opacity-[0.33] pb-meas16 text-center;
}

.marketplace-display-name-input-wrapper {
  @apply relative w-full;
}

.marketplace-display-name-note {
  @apply absolute text-desktop-caption1 font-normal text-white top-[1.2rem] md:whitespace-nowrap md:top-[0px] md:right-[0px];
}

.marketplace-display-name-input-label {
  @apply flex cursor-auto font-text-regular text-caption font-bold text-white;
}

.marketplace-display-name-label-container {
  @apply flex flex-col xs:mb-meas10 justify-between lg:flex-row lg:mb-meas0 md:flex-row md:mb-meas0 xl:flex-row xl:mb-meas0;
}
