import { Factory } from "fishery";
import Random from "../Random";
import { aCountryResponse } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MailingAddressPayload } from "@eait-playerexp-cn/creator-types";

const factory = Factory.define<MailingAddressPayload>(() => ({
  country: aCountryResponse(),
  state: Random.state(),
  city: Random.city(),
  zipCode: Random.zipCode(),
  street: Random.streetAddress()
}));

export function aMailingAddress(override = {}): MailingAddressPayload {
  return factory.build(override);
}
