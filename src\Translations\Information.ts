export const InformationLabels = {
  infoLabels: {
    interestedCreatorTitle: "Interested Creator",
    messages: {
      firstNameTooLong: "First name is too long",
      lastNameTooLong: "Last name is too long",
      street: "Street",
      streetTooLong: "Street is too long",
      city: "City",
      cityTooLong: "City is too long",
      state: "State",
      stateTooLong: "State is too long",
      zipCode: "Zip Code",
      zipCodeTooLong: "Zip Code is too long",
      tShirtSize: "T-Shirt Size",
      entityType: "Entity Type",
      businessName: "Business Name",
      businessNameTooLong: "Business Name is too long",
      email: "Email",
      emailTooLong: "Email is too long",
      emailInvalid: "Email is invalid",
      url: "URL",
      invalidUrl: "Invalid URL",
      followersMaxLength: "Followers is too long",
      firstName: "First Name",
      lastName: "Last Name",
      dateOfBirth: "Date of Birth",
      dateOfBirthInvalid: "Date of Birth is invalid",
      ageMustBe18OrOlder: "Age must be 18 or older",
      country: "Country",
      preferredNameTooLong: "Preferred Name is too long",
      preferredPronoun: "Preferred Pronoun",
      primaryPlatform: "Primary Platform"
    }
  },
  translation: {
    messages: {
      preferredEmailTooLong: "Preferred Email is too long",
      preferredEmailInvalid: "Preferred Email is invalid",
      preferredPhoneNumber: "Preferred Phone Number",
      preferredPhoneNumberTooLong: "Preferred Phone Number is too long",
      contentLanguage: "Content Language",
      language: "Language",
      preferredEmail: "Preferred Email"
    }
  },
  formLabels: {
    infoTitle: "Basic Information",
    firstName: "First Name",
    lastName: "Last Name",
    dateOfBirth: "Date of Birth",
    preferredEmail: "Preferred Email",
    eaEmailID: "EA Email ID",
    EAID: "EAID",
    country: "Country",
    street: "Street",
    state: "State",
    city: "City",
    zipCode: "Zip Code",
    contentMediaTitle: "Content Media",
    contentMediaDescription: "Tell us about your content media.",
    contentUrlPlaceholder: "Content URL",
    contentUrl: "Content URL",
    contentFollowers: "Content Followers",
    contentFollowersPlaceholder: "Content Followers",
    basicInformation: "Basic Information",
    addAnother: "Add Another",
    contentLanguagesTitle: "Content Languages",
    contentLanguagesDescription: "Tell us about the languages you create content in.",
    contentLanguage: "Content Language",
    languageTitle: "Language",
    languageDescription: "Tell us about the languages you create content in.",
    language: "Language",
    cancel: "Cancel",
    next: "Next",
    duplicateUrl: "Duplicate URL",
    urlScanFailed: "URL Scan Failed",
    followersMaxLength: "Followers is too long",
    selectCountry: "Select Country",
    remove: "Remove",
    ok: "OK",
    calendar: "Calendar",
    close: "Close",
    connectSocialMediaAccountTitle: "Connect Social Media Account",
    connectSocialMediaAccountDescription: "Connect your social media account to get started.",
    additionalContentAndWebsiteTitle: "Additional Content and Website",
    additionalContentAndWebsiteDescription: "Tell us about your additional content and website.",
    websiteUrlLabel: "Website URL",
    additionalLinkPlaceholder: "Additional Link",
    addMoreUrlLabel: "Add More URL",
    invalidUrl: "Invalid URL",
    ageMustBe18OrOlder: "Age must be 18 or older",
    preferredName: "Preferred Name",
    preferredPronouns: "Preferred Pronouns",
    preferredPronoun: "Preferred Pronoun",
    enterPronoun: "Enter Pronoun",
    selectProunoun: "Select Pronoun",
    platformPreferences: "Platform Preferences",
    platformPreferencesTitle: "Platform Preferences",
    primaryPlatform: "Primary Platform",
    secondaryPlatforms: "Secondary Platforms",
    secondaryPlatformsTitle: "Secondary Platforms",
    profilePicture: {
      termsAndConditionsLast: "Terms and Conditions Last",
      avatarRequired: "Avatar Required",
      message: "Message",
      termsAndConditionsFirst: "Terms and Conditions First",
      title: "Profile Picture",
      avatarInvalid: "Avatar Invalid",
      avatarMoreThanLimit: "Avatar More Than Limit",
      termsAndConditionsMiddle: "Terms and Conditions Middle"
    },
    browse: "Browse",
    labels: {
      infoTitle: "Basic Information",
      firstName: "First Name",
      lastName: "Last Name",
      dateOfBirth: "Date of Birth",
      preferredEmail: "Preferred Email",
      eaEmailID: "EA Email ID",
      EAID: "EAID",
      country: "Country",
      street: "Street",
      state: "State",
      city: "City",
      zipCode: "Zip Code",
      contentMediaTitle: "Content Media",
      contentMediaDescription: "Tell us about your content media.",
      contentUrlPlaceholder: "Content URL",
      contentUrl: "Content URL",
      contentFollowers: "Content Followers",
      contentFollowersPlaceholder: "Content Followers",
      basicInformation: "Basic Information",
      addAnother: "Add Another",
      contentLanguagesTitle: "Content Languages",
      contentLanguagesDescription: "Tell us about the languages you create content in.",
      contentLanguage: "Content Language",
      languageTitle: "Language",
      languageDescription: "Tell us about the languages you create content in.",
      language: "Language",
      cancel: "Cancel",
      next: "Next",
      duplicateUrl: "Duplicate URL",
      urlScanFailed: "URL Scan Failed",
      followersMaxLength: "Followers is too long",
      selectCountry: "Select Country",
      remove: "Remove",
      ok: "OK",
      calendar: "Calendar",
      close: "Close",
      connectSocialMediaAccountTitle: "Connect Social Media Account",
      connectSocialMediaAccountDescription: "Connect your social media account to get started.",
      additionalContentAndWebsiteTitle: "Additional Content and Website",
      additionalContentAndWebsiteDescription: "Tell us about your additional content and website.",
      websiteUrlLabel: "Website URL",
      additionalLinkPlaceholder: "Additional Link",
      addMoreUrlLabel: "Add More URL",
      invalidUrl: "Invalid URL",
      ageMustBe18OrOlder: "Age must be 18 or older",
      preferredName: "Preferred Name",
      preferredPronouns: "Preferred Pronouns",
      preferredPronoun: "Preferred Pronoun",
      enterPronoun: "Enter Pronoun",
      selectProunoun: "Select Pronoun",
      platformPreferences: "Platform Preferences",
      platformPreferencesTitle: "Platform Preferences",
      primaryPlatform: "Primary Platform",
      secondaryPlatforms: "Secondary Platforms",
      secondaryPlatformsTitle: "Secondary Platforms",
      profilePicture: {
        termsAndConditionsLast: "Terms and Conditions Last",
        avatarRequired: "Avatar Required",
        message: "Message",
        termsAndConditionsFirst: "Terms and Conditions First",
        title: "Profile Picture",
        avatarInvalid: "Avatar Invalid",
        avatarMoreThanLimit: "Avatar More Than Limit",
        termsAndConditionsMiddle: "Terms and Conditions Middle"
      },
      browse: "Browse"
    }
  },
  pageLabels: {
    interestedUserDescription2: "You can always change your preferences in your profile settings.",
    interestedUserDescription1: "You can always change your preferences in your profile settings.",
    confirmationDesc1: "You can always change your preferences in your profile settings.",
    confirmationDesc2: "You can always change your preferences in your profile settings.",
    modalConfirmationTitle: "Confirmation",
    creatorTitle: "Creator",
    yes: "Yes",
    no: "No",
    userDescription1: "You can always change your preferences in your profile settings.",
    userDescription2: "You can always change your preferences in your profile settings.",
    primaryPlatform: "Primary Platform",
    save: "Save"
  },
  layout: {
    buttons: {
      yes: "Yes",
      no: "No",
      gotit: "Got it",
      submitContent: "Submit Content",
      cancel: "Cancel",
      submit: "Submit",
      upload: "Upload",
      next: "Next",
      prev: "Prev",
      close: "Close",
      connect: "Connect",
      declineTermsAndCondition: "Decline Terms and Conditions"
    },
    toasts: {
      contentSubmittedTitle: "Content Submitted",
      contentSubmittedDescription: "Your content has been submitted successfully."
    },
    main: {
      pageNotFound: "Page Not Found",
      pageNotFoundContent: "The page you are looking for does not exist.",
      unauthorized: "Unauthorized",
      unhandledError: "Unhandled Error",
      unhandledErrorMessage: "An unhandled error occurred."
    },
    contentCard: {
      approvalNotRequired: "Approval Not Required",
      approved: "Approved",
      pendingApproval: "Pending Approval",
      rejected: "Rejected",
      submitted: "Submitted",
      video: "Video",
      changesRequired: "Changes Required",
      inReview: "In Review",
      viewChangesRequired: "View Changes Required",
      hideChangesRequired: "Hide Changes Required",
      sentOn: "Sent On",
      additionalDescription: "Additional Description",
      file: "File",
      url: "URL",
      update: "Update",
      from: "From",
      inScan: "In Scan"
    },
    header: {
      disclosure: "Disclosure"
    }
  }
};
