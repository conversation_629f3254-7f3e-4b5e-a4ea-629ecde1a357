import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AxiosResponse } from "axios";

export type SignerInformation = {
  businessName: string;
  creatorId: string;
  country: string;
  email: string;
  firstName: string;
  lastName: string;
  screenName: string;
  locale: string;
  program?: string;
};

class TermsAndConditionsService {
  constructor(private readonly client: TraceableHttpClient) {}

  async clearSignedStatusForProgram(program: string): Promise<AxiosResponse<void>> {
    return (await this.client.delete(
      `/api/terms-and-conditions/terms-and-conditions-status?program=${program}`
    )) as AxiosResponse<void>;
  }

  async getSigningUrl(signerInformation: SignerInformation): Promise<AxiosResponse<void>> {
    return (await this.client.post(`/api/terms-and-conditions/signing-url`, {
      body: signerInformation
    })) as AxiosResponse<void>;
  }
}

export default TermsAndConditionsService;
