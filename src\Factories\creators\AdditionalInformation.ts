import { Factory } from "fishery";
import { aHardwarePartner } from "./HardwarePartner";
import { aPointOfContact } from "./PointOfContact";
import Random from "../Random";
import { AdditionalInformationResponse } from "src/shared/creators/AdditionalInformation";

const factory = Factory.define<AdditionalInformationResponse>(() => ({
  hardwarePartners: [aHardwarePartner()],
  hoodieSize: Random.tShirtSize(),
  pointOfContact: aPointOfContact()
}));

export function aAdditionalInformation(override = {}): AdditionalInformationResponse {
  return factory.build(override);
}
