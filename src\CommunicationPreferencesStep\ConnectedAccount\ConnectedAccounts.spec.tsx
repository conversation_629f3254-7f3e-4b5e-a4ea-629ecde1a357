import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import { ConnectedAccount as Account, OnboardingStepConfiguration } from "../../types";
import { ConnectAccountLabels } from "../../Translations/ConnectAccounts";
import { ConnectedAccountComponent, ConnectedAccountProps } from "./ConnectedAccounts";
import { Labels } from "../CommunicationPreferencesStep";

describe("ConnectedAccount Component", () => {
  const accounts: Array<Account> = [
    {
      id: "1",
      accountId: "123",
      type: "FACEBOOK",
      username: "user1",
      name: "name1",
      uri: "uri1",
      thumbnail: "thumbnail1",
      isExpired: false,
      disconnected: false
    },
    {
      id: "2",
      accountId: "456",
      type: "YOUTUBE",
      username: "user2",
      name: "name2",
      uri: "uri2",
      thumbnail: "thumbnail2",
      isExpired: false,
      disconnected: false
    }
  ];
  const configuration = { metadataClient: {}, onBoardingClient: {} } as OnboardingStepConfiguration;
  const connectedAccountProps: ConnectedAccountProps = {
    labels: ConnectAccountLabels as unknown as Labels,
    setAccountToRemove: jest.fn(),
    accountToRemove: "",
    accounts: accounts,
    setShowAddConfirmation: jest.fn(),
    showAddConfirmation: false,
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    state: {},
    errorHandling: jest.fn(),
    stableDispatch: jest.fn(),
    configuration: configuration,
    connectAccounts: []
  };

  it("shows account connected", () => {
    render(<ConnectedAccountComponent {...connectedAccountProps} />);

    expect(screen.getByText(ConnectAccountLabels.myAccount)).toBeInTheDocument();
    expect(screen.getByText("user1")).toBeInTheDocument();
    expect(screen.getByText("user2")).toBeInTheDocument();
  });

  it("shows close remove account modal on cancel button click", () => {
    render(<ConnectedAccountComponent {...connectedAccountProps} showRemoveAccountModal={true} />);

    fireEvent.click(screen.getByText(ConnectAccountLabels.cancel));

    expect(connectedAccountProps.setShowRemoveAccountModal).toHaveBeenCalledWith(false);
  });
});
