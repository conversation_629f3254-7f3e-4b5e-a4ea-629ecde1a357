import React, { FC, memo, useCallback, useMemo, useRef, useState } from "react";
import { useRouter } from "next/router";
import {
  Button,
  edit,
  Icon,
  ModalBody,
  ModalClose<PERSON>utton,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { ErrorResponse, SESSION_USER } from "../../utils";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";
import { Dispatch, OnboardingStepConfiguration } from "../../types";
import { FormDataBody } from "@eait-playerexp-cn/http";

export type AvatarProps = {
  showModal: () => void;
  placeholderRef: (n: React.RefObject<HTMLButtonElement>) => void;
  src: string;
  updateAvatarLabel: string;
};
const Avatar: FC<AvatarProps> = ({ showModal, placeholderRef, src, updateAvatarLabel }) => {
  return (
    <>
      <div className="update-profile-picture-profile-card-logo">
        <img src={src} alt="" className="profile-card-avatar" />
      </div>
      <button
        type="button"
        className="update-profile-picture-profile-card-logo-edit"
        ref={placeholderRef as unknown as React.RefObject<HTMLButtonElement>}
        onClick={showModal}
        aria-label={updateAvatarLabel}
      >
        <Icon icon={edit} />
      </button>
    </>
  );
};

export type PicturePlaceholderProps = {
  showModal: () => void;
  placeholderRef: (n: React.RefObject<HTMLButtonElement>) => void;
  src: string | null;
  updateAvatarLabel: string;
};
const PicturePlaceholder: FC<PicturePlaceholderProps> = ({ showModal, placeholderRef, src, updateAvatarLabel }) => {
  return (
    <section
      className="update-profile-picture-placeholder"
      ref={placeholderRef as unknown as React.RefObject<HTMLButtonElement>}
    >
      <Avatar
        showModal={showModal}
        placeholderRef={placeholderRef}
        src={src as string}
        updateAvatarLabel={updateAvatarLabel}
      />
    </section>
  );
};

export type FooterButtonsProps = {
  buttons: { save: string; cancel: string };
  onClose: () => void;
  onSave: (e: React.ChangeEvent<HTMLInputElement>) => void;
  cancelButtonRef: React.RefObject<HTMLButtonElement>;
  disabled: boolean;
  isLoader: boolean;
};

const FooterButtons: FC<FooterButtonsProps> = memo(function FooterButtons({
  buttons: { save, cancel },
  onClose,
  onSave,
  cancelButtonRef,
  disabled,
  isLoader
}) {
  return (
    <>
      <Button variant="tertiary" dark size="sm" ref={cancelButtonRef} onClick={onClose}>
        {cancel}
      </Button>
      <Button
        disabled={disabled}
        spinner={isLoader}
        size="sm"
        type="submit"
        onClick={(e) => {
          onSave(e as unknown as React.ChangeEvent<HTMLInputElement>);
        }}
      >
        {save}
      </Button>
    </>
  );
});

type ModalBodyTransalte = {
  messages: {
    title: string;
    message: string;
    termsAndConditionsFirst: string;
    termsAndConditionsMiddle: string;
    termsAndConditionsLast: string;
  };
  buttons: { browse: string; save: string; cancel: string };
};

export type ModalBodyContentProps = {
  translate: ModalBodyTransalte;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  selector: string;
  previewSrc: string | null;
  size: number;
  invalidImage: string | null;
  defaultImage?: string;
};

const ModalBodyContent: FC<ModalBodyContentProps> = memo(function ModalBodyContent({
  translate,
  onChange,
  selector,
  previewSrc,
  size,
  invalidImage,
  defaultImage
}) {
  const router = useRouter();
  return (
    <div className="update-profile-picture-container">
      <div className="update-profile-picture-upload-section">
        <div className="update-profile-picture-placeholder-title">{translate.messages.message}</div>
        <button className="btn btn-secondary btn-dark">
          <input type="file" id={selector} name={selector} hidden onChange={onChange} size={size} />
          <label htmlFor={selector} className="update-profile-picture-label">
            {translate.buttons.browse}
          </label>
        </button>
        {invalidImage && <div className="form-error-message">{invalidImage}</div>}
        <div className="update-profile-picture-placeholder-terms">
          {translate.messages.termsAndConditionsFirst}{" "}
          <a
            target="_blank"
            rel="noreferrer"
            href={`${
              router.locale === "en-us"
                ? "https://tos.ea.com/legalapp/WEBTERMS/US/en/PC/"
                : "https://tos.ea.com/legalapp/WEBTERMS/US/ja/PC/"
            }`}
            className="update-profile-picture-agreement-link"
          >
            {translate.messages.termsAndConditionsMiddle}
          </a>{" "}
          {translate.messages.termsAndConditionsLast}
        </div>
      </div>
      <div className="update-profile-picture-image-placeholder">
        <img src={previewSrc || defaultImage} className="update-profile-picture-preview" alt="Avatar Preview" />
      </div>
    </div>
  );
});

type profilePictureLabels = {
  title: string;
  avatarRequired: string;
  avatarInvalid: string;
  avatarMoreThanLimit: string;
  message: string;
  termsAndConditionsFirst: string;
  termsAndConditionsMiddle: string;
  termsAndConditionsLast: string;
};

export type UpdateProfilePictureLabels = {
  buttons: { browse: string; save: string; cancel: string; close: string };
  profilePicture: profilePictureLabels;
  profileLabels: { updateAvatar: string };
};

export type UpdateProfilePictureProps = {
  selector?: string;
  size?: number;
  src: string | null;
  labels?: UpdateProfilePictureLabels;
  isPlaceholderDefault?: boolean;
  user: { avatar: string };
  stableDispatch: Dispatch;
  defaultImage?: string;
  configuration: OnboardingStepConfiguration;
};

export const UpdateProfilePicture: FC<UpdateProfilePictureProps> = ({
  selector = "upload",
  size = 1048576,
  src,
  labels,
  isPlaceholderDefault = true,
  user,
  stableDispatch,
  defaultImage,
  configuration
}) => {
  const [isShown, setIsShown] = useState(false);
  const [profilePicturePlaceholder, setProfilePicturePlaceholder] = useState<null | React.RefObject<HTMLButtonElement>>(
    null
  );
  const [previewSrc, setPreviewSrc] = useState<null | string>(null);
  const [invalidImage, setInvalidImage] = useState<null | string>(null);
  const [uploadedSrc, setUploadedSrc] = useState<string | null>(src);
  const [isFilePicked, setIsFilePicked] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>();
  const { translate, updateAvatarLabel } = useMemo(() => {
    return {
      translate: {
        buttons: labels?.buttons,
        messages: labels?.profilePicture as profilePictureLabels,
        close: labels?.buttons.cancel,
        browse: labels?.buttons.browse
      },
      updateAvatarLabel: labels?.profileLabels.updateAvatar
    };
  }, [labels]);
  const showModal = () => {
    setIsShown(true);
    setPreviewSrc(uploadedSrc);
  };
  const [isLoader, setIsLoader] = useState(false);
  const { creatorsClient, DEFAULT_AVATAR_IMAGE: defaultAvatar } = configuration;
  const creatorService = creatorsClient ? new CreatorService(creatorsClient, defaultAvatar) : undefined;

  const closeModal = () => {
    setIsShown(false);
    setIsFilePicked(false);
    setPreviewSrc(null);
    setInvalidImage(null);
    (profilePicturePlaceholder as unknown as HTMLElement)?.focus();
  };
  const changeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file: File = (event.target as unknown as { files: Array<File> }).files[0];
    if (file === undefined) {
      setInvalidImage(translate.messages.avatarRequired);
      return false;
    }
    if (!(file?.type === "image/jpeg" || file?.type === "image/png" || file?.type === "image/gif")) {
      setInvalidImage(translate.messages.avatarInvalid);
      return false;
    }

    if (file?.size > size) {
      setInvalidImage(translate.messages.avatarMoreThanLimit);
      return false;
    }
    setInvalidImage(null);
    setSelectedFile(file);
    setPreviewSrc(URL.createObjectURL(file));
    setIsFilePicked(true);
  };
  const submitHandler = async (event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault();
    setIsLoader(true);
    if (previewSrc !== null) {
      const formData = new FormData();
      formData.append("avatar", selectedFile as File, (selectedFile as File).name);
      if (creatorService) {
        creatorService
          .updateAvatar(formData as unknown as FormDataBody)
          .then(() => {
            setUploadedSrc(previewSrc);
            setIsLoader(false);
            closeModal();
            // Update the user prop. So that the new avatar gets rendered across components
            const extension = (selectedFile as File).name.split(".").pop();
            user.avatar =
              user.avatar.split(".").slice(0, -1).join(".").slice(0) + `.${extension}?t=${new Date().getTime()}`;
            stableDispatch({ type: SESSION_USER, data: user });
          })
          .catch((e: ErrorResponse) => {
            setIsLoader(false);
            if (e?.response?.status === 422) {
              setInvalidImage((e.response.data as unknown as { errors: { avatar: string } }).errors.avatar);
            } else {
              setInvalidImage((e as unknown as { message: string }).message);
            }
          });
      }
    }
  };

  const isInvalidFormatPicked = useCallback(() => isFilePicked && invalidImage, [isFilePicked, invalidImage]);
  const cancelButtonRef = useRef(null);

  return (
    <>
      {isPlaceholderDefault && (
        <PicturePlaceholder
          showModal={showModal}
          placeholderRef={(n) => setProfilePicturePlaceholder(n)}
          src={uploadedSrc}
          updateAvatarLabel={updateAvatarLabel as string}
        />
      )}
      {!isPlaceholderDefault && (
        <div className="profile-card-logo-container">
          <Avatar
            showModal={showModal}
            placeholderRef={(n) => setProfilePicturePlaceholder(n)}
            src={uploadedSrc as string}
            updateAvatarLabel={updateAvatarLabel as string}
          />
        </div>
      )}
      {isShown && (
        <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{translate.messages.title}</ModalTitle>
            <ModalCloseButton
              ariaLabel={translate?.buttons?.close as string}
              closeButtonRef={cancelButtonRef}
            ></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <ModalBodyContent
              selector={selector}
              translate={translate as ModalBodyTransalte}
              previewSrc={previewSrc}
              size={size}
              invalidImage={invalidImage}
              defaultImage={defaultImage}
              onChange={changeHandler}
            />
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons
              buttons={translate.buttons as { save: string; cancel: string }}
              onClose={closeModal}
              onSave={submitHandler}
              cancelButtonRef={cancelButtonRef}
              disabled={(!isFilePicked || isLoader || isInvalidFormatPicked()) as boolean}
              isLoader={isLoader}
            />
          </ModalFooter>
        </ModalV2>
      )}
    </>
  );
};

export default UpdateProfilePicture;
