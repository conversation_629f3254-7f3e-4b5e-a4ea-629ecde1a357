import React, { <PERSON>, memo, Mouse<PERSON>ventHandler, ReactElement, ReactNode, useCallback, useEffect, useState } from "react";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../../utils";
import {
  BrowserAnalytics,
  CloseHandler,
  Dispatch,
  ErrorHandling,
  OnboardingStepConfiguration,
  State,
  ValidationError
} from "../../types";
import { NextRouter } from "next/router";
import { SvgProps, Toast } from "@eait-playerexp-cn/core-ui-kit";
import { AxiosError } from "axios";
import CreatorTypeForm, { CreatorTypeFormLabels } from "../CreatorTypeForm/CreatorTypeForm";
import CancelRegistrationModal from "src/common/CancelRegistrationModal/CancelRegistrationModal";
import { Url } from "next/dist/shared/lib/router/router";
import { C<PERSON>Type, FormLabels, PageLabels } from "../creator-type";
import Form from "src/utils/Form";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import CreatorWithProgramCode from "src/shared/creators/CreatorWithProgramCode";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import CreatorService from "@src/Browser/CreatorService";

export type CreatorTypePageProps = {
  formLabels: FormLabels & CreatorTypeFormLabels;
  pageLabels: PageLabels;
  showConfirmation?: boolean;
  state: State;
  stableDispatch: Dispatch;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  unhandledError?: string;
  setShowConfirmation: (a: boolean) => void;
  router: NextRouter;
  locale: string;
  dispatch: Dispatch;
  analytics: BrowserAnalytics;
  errorHandling: ErrorHandling;
  onClose: MouseEventHandler<HTMLButtonElement>;
  configuration: OnboardingStepConfiguration;
  setShowMigration: (value: boolean) => void;
  navigateToPage: Url;
  showMigration: boolean;
  PROGRAM_CODE: string;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  basePath?: string;
  onContinueToNextPage?: () => void;
};

export type NavStep = {
  icon: FC<SvgProps>;
  title: string;
  href: string;
  isCompleted?: boolean;
};

export default memo(function CreatorTypePage({
  formLabels,
  pageLabels,
  onClose,
  showConfirmation = false,
  stableDispatch,
  dispatch,
  unhandledError = "",
  state,
  errorToast,
  setShowConfirmation,
  setShowMigration,
  router,
  analytics,
  errorHandling,
  configuration,
  showMigration,
  navigateToPage,
  breadcrumbLabels,
  PROGRAM_CODE,
  basePath,
  onContinueToNextPage
}: CreatorTypePageProps) {
  const { creatorDescription, creatorTitle } = pageLabels;
  const { isError = false, isValidationError = false, onboardingSteps = [] } = state;
  const [creatorTypes, setCreatorTypes] = useState<CreatorType[] | null>(null);
  const [creator, setCreator] = useState<(Record<string, unknown> & { creatorTypes: string[] }) | null>(null);
  const metadataService = new MetadataService(configuration.metadataClient);
  const creatorService = configuration.creatorsClient
    ? new CreatorsService(configuration.creatorsClient, configuration.DEFAULT_AVATAR_IMAGE)
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? new CreatorService(configuration.onBoardingClient)
    : undefined;

  const handleCancelRegistration = useCallback(() => {
    if (analytics.canceledOnboardingFlow) {
      analytics.canceledOnboardingFlow({ locale: router.locale ?? "", page: router.pathname });
    }
    router.push("/api/logout");
  }, [router]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const submitHandle: any = useCallback(
    async (data: CreatorWithProgramCode, navigateBack: boolean, navigateToPage: Url) => {
      try {
        stableDispatch({ type: USER_NAVIGATED, data: false });
        const currentStep = (onboardingSteps as Array<NavStep>).find((step) => step.href === router.pathname);
        const payload = {
          ...data,
          creatorConnectedProgram: PROGRAM_CODE
        };
        const values = data.creatorTypes.map((item) => (item as unknown as { value: string }).value);
        if (legacyCreatorService) {
          await legacyCreatorService.update(
            payload as unknown as UpdateCreatorRequest & Partial<CreatorWithProgramCode>
          );
        } else {
          await (creatorService as CreatorsService).updateCreator({
            creatorTypes: values,
            program: { code: PROGRAM_CODE }
          } as UpdateCreatorRequest);
        }
        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        if (creator) {
          creator.creatorTypes = (data.creatorTypes as unknown as CreatorType[]).map(
            (creatorType) => creatorType.value
          );
        }
        if (analytics.confirmedCreatorType) {
          analytics.confirmedCreatorType({
            locale: router.locale ?? "",
            creator: creator as Record<string, unknown>
          });
        }
        // Navigate to next page using callback or fallback to default navigation
        if (onContinueToNextPage) {
          onContinueToNextPage();
        } else {
          if (navigateToPage) {
            router.push(navigateToPage);
          } else if (navigateBack) {
            router.push("/onboarding/information");
          } else {
            router.push("/franchises-you-play");
          }
        }
      } catch (e) {
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    },
    [creatorTypes, stableDispatch, creator, router, onboardingSteps]
  );

  const { pending: isPending, execute: submitHandleClb } = useAsync(submitHandle, false);

  useEffect(() => {
    async function fetchData() {
      try {
        stableDispatch({ type: LOADING, data: true });
        const creator = legacyCreatorService
          ? (await legacyCreatorService.getCreatorWithProgramCode()).data
          : await creatorService?.getCreator(PROGRAM_CODE);
        setCreator(creator as unknown as Record<string, unknown> & { creatorTypes: string[] });

        const creatorType = (await metadataService.getCreatorTypes()) as unknown as CreatorType[];
        if (creatorType) {
          setCreatorTypes(creatorType);
        }
        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError || toastContent(isValidationError as unknown as ValidationError[])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const modalLabels = {
    title: pageLabels.modalConfirmationTitle,
    yes: formLabels.yes,
    no: formLabels.no,
    close: formLabels.close,
    confirmationDesc1: pageLabels.confirmationDesc1,
    confirmationDesc2: pageLabels.confirmationDesc2
  };

  return (
    <div className="mg-page-onboarding-creator">
      <section className="creator-types-container-creator-type-container">
        <div className="mg-intro">
          <h3 className="creator-types-container-creator-type-title">{creatorTitle}</h3>
        </div>
        <div className="creator-types-container-creator-type-description">{creatorDescription}</div>
        {creatorTypes && creator && (
          <Form mode="onChange" key="creatorType" onSubmit={submitHandleClb}>
            <CreatorTypeForm
              formLabels={formLabels}
              router={router}
              onClose={onClose}
              creatorsType={creatorTypes}
              stableDispatch={stableDispatch}
              submitHandle={submitHandle}
              showMigration={showMigration}
              setShowMigration={setShowMigration}
              creator={creator}
              state={state}
              navigateToPage={navigateToPage}
              isPending={isPending}
              breadcrumbLabels={breadcrumbLabels}
              dispatch={dispatch}
              basePath={basePath}
            />
          </Form>
        )}
        {showConfirmation && (
          <CancelRegistrationModal
            handleModalClose={handleModalClose}
            handleCancelRegistration={handleCancelRegistration}
            labels={modalLabels}
          />
        )}
      </section>
    </div>
  );
});
