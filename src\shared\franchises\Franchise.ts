export type FranchiseResponse = { id: string; name: string; boxArtUrl: string; type: string };

export default class Franchise {
  readonly image: string;

  static fromApi(data: FranchiseResponse): Franchise {
    return new Franchise(data.id, data.name, data.boxArtUrl, data.type);
  }

  constructor(
    readonly value: string,
    readonly label: string,
    boxArtUrl?: string,
    readonly type?: string
  ) {
    this.image = boxArtUrl || "";
  }

  isPrimary(): boolean {
    return this.type === "PRIMARY";
  }
}
