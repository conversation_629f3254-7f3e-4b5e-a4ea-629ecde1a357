// import { InfoLabels, PageLabe<PERSON> } from "@components/forms/creatorCodeForm/CreatorCodeForm";

// export const pageLabels: PageLabels = {
//   title: "Setup Your Unique Creator Code",
//   description:
//     "Create a unique code that represents your brand or identity. This code will help fans and followers recognize you easily. Ensure your code reflects your content and is simple for your audience to remember.",
//   tips: {
//     title: "Tips for creating a great code:",
//     labels: ["Choose something that aligns with your persona or channel name.", "Keep it concise and memorable."]
//   },
//   validationInfo:
//     "Creator codes can only contain letters and numbers, up to a maximum of 22 characters, with no special symbols",
//   inputCustomizeCodeLabel: "Customize your code",
//   inputConfirmCodeLabel: "Confirm your code",
//   next: "Next",
//   cancel: "Cancel"
// };

// export const infoLabels: InfoLabels = {
//   messages: {
//     customizeCodeRequired: "Creator code is required",
//     confirmCodeRequired: "Confirm code is required",
//     tooLong: "Creator codes can be up to 22 characters long",
//     specialCharactersNotAllowed: "Only characters and numbers are allowed. No special characters or spaces.",
//     doNotMatch: "Codes do not match. Please try again.",
//     alreadyInUse: "This code is already in use. Please try a different one.",
//     codeIsInvalid: "The creator code is invalid. It should only contain alphanumeric characters.",
//     inappropriateLanguage: "This code contains inappropriate language. Please choose a different code."
//   }
// };
