.terms-and-conditions {
  @apply flex flex-col items-center max-w-full m-meas8;
}

.terms-and-conditions .input-box {
  @apply border-none;
}

.terms-and-conditions input[type="text"] {
  @apply w-full md:w-[290px] xl:w-[368px];
}
.terms-and-conditions .terms-condition-address > .address-cont > .select-box {
  @apply w-full md:w-[290px] xl:w-[368px];
}

.terms-and-conditions .select-header-title,
.select-header-label {
  @apply w-full;
}
.terms-and-conditions .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}

.terms-and-conditions > main {
  @apply flex flex-col items-center justify-center max-w-full;
}

.terms-and-conditions > main > header {
  @apply flex flex-col items-center justify-center md:pb-meas24 pb-meas12;
}

.terms-and-conditions > main > header > h3 {
  @apply font-bold text-center xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 font-display-regular text-gray-10 pb-meas12;
}

.terms-and-conditions > main > header > p {
  @apply font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large md:max-w-[640px] xl:max-w-[1024px] xl:w-[80%] text-center text-gray-10;
}

.terms-and-conditions .pactsafe {
  @apply flex justify-center max-w-full xs:min-w-full;
}
.terms-and-conditions .pactsafe > form {
  @apply w-full max-w-full;
}
.terms-and-conditions .pactsafe .btn-tertiary {
  @apply mt-meas8;
}
.terms-and-conditions .pactsafe > .pactSafe-iframe-cont {
  @apply flex flex-col items-end xs:min-w-full md:min-w-[auto];
}

.terms-and-conditions .pactsafe > .pactSafe-iframe-cont > hr {
  @apply w-[100%];
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
.terms-and-conditions .pactsafe > .pactSafe-iframe-cont > iframe {
  @apply min-w-full my-meas26 min-h-[461px] md:min-w-[641px] md:min-h-[436px] xl:min-w-[850px] xl:min-h-[695px];
}

.terms-and-conditions form .input-box-label {
  @apply text-gray-10 mr-meas16;
}

.terms-and-conditions form .input-box-label-disabled {
  @apply text-desktop-body-default;
}

.terms-and-conditions form label.form-input-box {
  @apply flex items-center w-[90%];
}

.input-help-text {
  @apply w-full md:w-[290px] xl:w-[368px];
}
.terms-and-conditions form .terms-condition-personal label.form-input-box {
  @apply items-start mb-meas12 md:mb-meas0;
}

.terms-and-conditions > main > section.pactsafe > form {
  @apply py-meas16;
}

.terms-and-conditions .pact-safe-entity-form input:disabled {
  @apply text-gray-10 pl-meas0 text-ellipsis;
  background-color: transparent;
}

.terms-and-conditions .pact-safe-entity-form .ob-footer-container {
  @apply xs:mr-meas10;
}

.terms-and-conditions .pact-safe-entity-form .input-container:hover {
  background-color: transparent;
}

.terms-and-conditions .pact-safe-entity-form .input-box:hover {
  background: none;
}
.terms-and-conditions .pact-safe-entity-form .terms-condition-entity {
  @apply py-meas20;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.terms-and-conditions .pact-safe-entity-form .personal-info-address-container {
  @apply md:border-t border-white border-opacity-[0.3];
}
.terms-and-conditions .pact-safe-entity-form .terms-condition-entity .form-radio-field {
  @apply flex-row w-full;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-entity .form-radio-field .radio-container {
  @apply grid grid-cols-2 md:flex-row md:justify-between md:w-full gap-meas20;
}

.terms-condition-entity .fieldset {
  @apply flex flex-col md:flex-row font-text-regular;
}

.terms-condition-entity .fieldset > legend {
  @apply font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default text-gray-10 xs:mt-meas7;
}

.terms-condition-entity aside {
  @apply flex justify-between mt-meas8 md:ml-meas20;
}

.terms-condition-personal {
  @apply md:pt-meas26;
}
.terms-condition-personal .personal-input-cont {
  @apply flex justify-start md:pb-meas12;
}

.terms-condition-personal .personal-input-cont > label {
  @apply flex flex-col items-start mb-meas12 w-full md:w-[auto];
}

.terms-condition-personal .input-box.input-box-disabled input {
  @apply p-meas0;
  background: none;
}

.terms-condition-entity .radio-container label {
  @apply flex flex-row;
}

.terms-and-conditions form .terms-condition-address .checkbox-label-container {
  @apply flex-row text-white;
}

.terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div > .input-box-label {
  @apply flex flex-col items-start pb-meas12;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .input-box {
  @apply flex flex-col md:flex-row;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .input-box-label {
  @apply font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default w-[210px];
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .form-input-box input {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.terms-condition-address > div.address-cont {
  @apply flex flex-col md:grid md:mt-meas16 mt-meas10;
}
@media screen and (min-width: 768px) {
  .terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont:not(.business),
  .terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div.address-cont {
    grid-template-columns: 50% 50%;
    display: grid;
  }
  .terms-condition-address > .legal-entity-field.asMailingAddress ~ div.address-cont {
    grid-template-columns: 100%;
  }
}
@media screen and (min-width: 1024px) {
  .terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont:not(.business),
  .terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div.address-cont {
    display: grid;
    grid-template-columns: 45% 45%;
    gap: 2rem;
  }
}

.terms-condition-address > .legal-entity-field.asMailingAddress ~ div.address-cont {
  grid-template-columns: 100%;
}

.terms-and-conditions .pact-safe-entity-form .input-box:not(.input-box-disabled) {
  @apply w-full;
}

.terms-and-conditions .pact-safe-entity-form .input-box.input-box-disabled {
  border: none;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont {
  @apply flex flex-col md:flex-row;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont.business {
  @apply flex-col md:items-start;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont.business .business-caption {
  @apply xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1 font-text-regular w-full md:w-[316px] xl:w-[319px] mb-meas8;
}

.terms-and-conditions .pact-safe-entity-form .input-box {
  @apply md:w-auto md:min-w-full;
}
.terms-and-conditions form .terms-condition-address label {
  @apply flex-col pb-meas8 w-full md:w-auto;
}

.terms-condition-address > .address-cont > .select-box {
  @apply pb-meas16;
}

.terms-condition-address > .address-cont > .select-box > .select-error-message {
  @apply text-error-50;
}

.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > label {
  @apply flex flex-col md:flex-row w-full xl:w-[80%] items-start md:items-center md:justify-between;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > label .input-box {
  @apply h-meas12 font-text-regular p-meas0 max-w-[319px];
  min-width: auto;
}
.terms-condition-address {
  @apply md:mt-meas20;
}
.pact-safe-entity-form {
  @apply xl:mb-[120px] md:mb-[70px] w-full max-w-full xl:w-[80%];
}
.terms-and-condition-title {
  @apply font-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 font-display-regular;
}
.terms-and-conditions-description {
  @apply text-desktop-body-default font-text-regular;
}
.terms-and-conditions-decline {
  @apply font-display-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default text-gray-10 mt-meas4;
  background: transparent;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > label > .input-error-message {
  grid-column: 12;
}
