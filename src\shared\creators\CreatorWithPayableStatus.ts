import PreferredFranchise from "../franchises/PreferredFranchise";
import MailingAddress from "./MailingAddress";
import PreferredPlatform from "../platforms/PreferredPlatform";
import CommunicationPreferences from "./CommunicationPreferences";
import AdditionalInformation from "./AdditionalInformation";
import LegalEntityInformation from "./LegalEntityInformation";
import CreatorWithExpiredAccounts, { CreatorWithExpiredAccountsResponse } from "./CreatorWithExpiredAccounts";
import ConnectedAccount from "../channels/ConnectedAccount";
import AccountInformationWithPayableStatus, {
  AccountInformationWithPayableInfoResponse
} from "./AccountInformationWithPayableStatus";
import { Overwrite } from "../../types";

export type CreatorWithPayableStatusResponse = Overwrite<
  CreatorWithExpiredAccountsResponse,
  {
    accountInformation: AccountInformationWithPayableInfoResponse;
  }
>;

export default class CreatorWithPayableStatus extends CreatorWithExpiredAccounts {
  static fromApi(data: CreatorWithPayableStatusResponse): CreatorWithPayableStatus {
    return new CreatorWithPayableStatus(
      data.id,
      data.avatar,
      data.creatorTypes || [],
      AccountInformationWithPayableStatus.fromApi(data.accountInformation),
      data.preferredPlatforms.map((item) => PreferredPlatform.fromApi(item)),
      data.preferredFranchises.map((item) => PreferredFranchise.fromApi(item)),
      MailingAddress.fromApi(data.mailingAddress),
      CommunicationPreferences.fromApi(data.communicationPreferences),
      data.connectedAccounts || [],
      AdditionalInformation.fromApi(data.additionalInformation),
      data.legalInformation ? LegalEntityInformation.fromApi(data?.legalInformation) : undefined
    );
  }

  constructor(
    readonly id: string,
    readonly avatar: string | null,
    readonly creatorTypes: Array<string>,
    public accountInformation: AccountInformationWithPayableStatus,
    preferredPlatforms: Array<PreferredPlatform>,
    preferredFranchises: Array<PreferredFranchise>,
    readonly mailingAddress: MailingAddress,
    readonly communicationPreferences: CommunicationPreferences,
    readonly connectedAccounts: Array<ConnectedAccount>,
    readonly additionalInformation: AdditionalInformation,
    readonly legalEntity?: LegalEntityInformation
  ) {
    super(
      id,
      avatar,
      creatorTypes,
      accountInformation,
      preferredPlatforms,
      preferredFranchises,
      mailingAddress,
      communicationPreferences,
      connectedAccounts,
      additionalInformation,
      legalEntity
    );
  }

  isPayable(): boolean {
    return this.accountInformation?.isPayable || false;
  }

  status(): string {
    return this.accountInformation?.status || "";
  }

  hasIsFlaggedInformation(): boolean {
    return false;
  }
}
