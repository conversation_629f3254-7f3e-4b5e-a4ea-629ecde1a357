.communication-preferences-container {
  @apply w-11/12 md:w-[640px] xl:w-[672px];
}
.communication-preferences-container .onboarding-intro {
  @apply flex justify-center pb-meas16 flex-col;
}
.communication-preferences-container > form {
  width: 100% !important;
}
.onboarding-mg-communication-row {
  @apply flex w-full flex-col items-start justify-start border-t border-white border-opacity-[0.33] py-meas16;
}
.onboarding-mg-communication-row > .form-input-box,
.onboarding-mg-communication-row > .select-box {
  @apply mx-meas0 w-full md:w-[320px] !important;
}
.onboarding-mg-communication-row .input-box-label,
.onboarding-mg-communication-row .select-label {
  @apply xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1 font-text-regular text-gray-10;
} /* Adding parent class for .select-label as we should not override core-ui-kit select label without parent class. It will impact globally*/
.onboarding-mg-communication-title {
  @apply xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 flex flex-row pb-meas6 font-display-regular font-bold;
}

.onboarding-mg-communication-preference-field {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default col-span-3 flex w-full break-all font-text-regular text-white md:w-[320px];
}
.onboarding-mg-communication-preference-field > label,
.onboarding-mg-communication-preference-field .input-box,
.onboarding-mg-communication-preference-field .select-box {
  @apply w-full;
}
.onboarding-mg-communication-preference-field .select-header-title,
.onboarding-mg-communication-preference-field .select-header-label {
  @apply w-full;
}
.onboarding-mg-communication-preference-field .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.onboarding-mg-communication-description {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default flex flex-row pb-meas18 font-text-regular;
}
.onboarding-mg-communication-row .select-header {
  @apply h-meas20;
}
.onboarding-mg-communication-row .select-header-title,
.select-header-label {
  @apply w-full;
}
.onboarding-mg-communication-row .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.btn-discord > .icon-block .icon {
  @apply h-meas10 w-meas10;
}
.onboarding-mg-communication-row.preferred-email {
  @apply pb-[30px] pt-meas22;
}
.onboarding-mg-communication-row.preferred-phone-number,
.onboarding-mg-communication-row.language {
  @apply pb-[30px] pt-[42px];
}
.onboarding-mg-communication-title.preferred-phone-number,
.onboarding-mg-communication-description.content-language,
.onboarding-mg-communication-description.language {
  @apply pb-meas13 md:pb-meas8;
}
.onboarding-mg-communication-row.content-language {
  @apply pb-[39px] pt-[42px];
}
.onboarding-mg-communication-description.preferred-email {
  @apply pb-meas13;
}

.onboarding-mg-communication-row.discord .onboarding-mg-connected-accounts-container {
  border-top: none;
}

.onboarding-mg-communication-row.discord > button {
  @apply flex items-center justify-center;
}

.onboarding-mg-communication-row.discord > .btn-primary > span {
  @apply ml-meas4;
}

.onboarding-mg-communication-row.discord
  .onboarding-mg-connected-accounts-container
  .onboarding-mg-connected-accounts-title {
  @apply hidden;
}

.onboarding-mg-communication-row.discord .connected-acc-card-container .empty-card {
  box-shadow:
    rgba(115, 204, 117, 0.8) 0 1px 4px,
    rgba(115, 204, 117, 0.8) 0 0 1px 4px;
  width: 231px;
  height: 231px;
}
.communication-preferences-page{
  @apply pb-meas16;
}