import { Factory } from "fishery";
import Random from "../Random";
import { aCountryResponse } from "@eait-playerexp-cn/metadata-test-fixtures";
import { LegalEntityInformationResponse } from "src/shared/creators/LegalEntityInformation";

const factory = Factory.define<LegalEntityInformationResponse>(() => ({
  businessName: Random.companyName(),
  city: Random.city(),
  country: aCountryResponse(),
  entityType: Random.entityType(),
  state: Random.state(),
  street: Random.streetAddress(),
  zipCode: Random.zipCode()
}));

export function aLegalEntity(override = {}): LegalEntityInformationResponse {
  return factory.build(override);
}
