import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import CheckboxCards from "./CheckboxCards";

describe("CheckboxCard Component", () => {
  const onChange = jest.fn();
  const items = [
    { id: 1, label: "Item 1", checked: false },
    { id: 2, label: "Item 2", checked: false }
  ];
  const value = [items[0]];
  const basePath = "/support-a-creator";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly", () => {
    render(<CheckboxCards items={items} value={value} onChange={onChange} basePath={basePath} />);

    expect(screen.getByText("Item 1")).toBeInTheDocument();
    expect(screen.getByText("Item 2")).toBeInTheDocument();
  });

  it("calls onChange handler when checkbox is clicked", () => {
    render(<CheckboxCards items={items} value={value} onChange={onChange} basePath={basePath} />);

    fireEvent.click(screen.getByLabelText("Item 1"));

    expect(onChange).toHaveBeenCalled();
  });
});
