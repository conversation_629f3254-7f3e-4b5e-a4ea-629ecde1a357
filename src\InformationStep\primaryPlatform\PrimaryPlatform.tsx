import { Controller, useFormContext } from "react-hook-form";
import { Option, Select } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import CheckboxCards from "../../common/cards/CheckboxCards/CheckboxCards";
import Card from "../../common/cards/Card/Card";
import { Labels } from "../OnboardingInformationStep";
import { PlatformOption } from "../../types";

type PrimaryPlatformProps = {
  primaryPlatform: Option | null;
  secondaryPlatforms: Option[];
  options: PlatformOption[];
  labels: Labels;
};

export default memo(function PrimaryPlatform({
  primaryPlatform,
  secondaryPlatforms,
  options = [],
  labels
}: PrimaryPlatformProps) {
  const methods = useFormContext();
  const { control, setValue } = methods;
  const { pageLabels, formLabels } = labels;
  const infoLabels = {
    ...formLabels,
    primaryPlatformRequired: labels.infoLabels.messages.primaryPlatform
  };
  const [platformIcon, setPlatformIcon] = useState<string | null>(null);
  const secondaryProps = useMemo(
    () => ({
      name: "secondaryPlatforms",
      disabled: platformIcon === null,
      items: options.map((item) => {
        return {
          ...item,
          checked: secondaryPlatforms && secondaryPlatforms.filter((values) => values.value === item.value).length > 0
        };
      })
    }),
    [platformIcon, options, secondaryPlatforms]
  );
  useEffect(() => {
    if (options.length > 0) {
      const initialValue = primaryPlatform || options[0];
      setValue("primaryPlatform", initialValue);
    }
  }, [options, primaryPlatform, setValue]);

  useEffect(() => {
    if (primaryPlatform) {
      const matchingOption = options.find((item: PlatformOption) => item.value === primaryPlatform.value);
      if (matchingOption) {
        setPlatformIcon(matchingOption.imageAsIcon);
      }
    } else {
      setPlatformIcon(options[0]?.imageAsIcon);
    }
  }, [primaryPlatform, options]);

  const primaryCardOnChange = (selectedItem: PlatformOption) => {
    setPlatformIcon(selectedItem.imageAsIcon);
  };

  const onPrimaryCardChange = useCallback(
    (item: PlatformOption, field: { onChange: (value: PlatformOption) => void }) => {
      primaryCardOnChange(item);
      field.onChange(item);
    },
    []
  );
  return (
    <>
      <div className="onboarding-mg-platform-container">
        <div className="mg-intro">
          <h4 className="onboarding-mg-platform-title">{infoLabels.platformPreferences}</h4>
          <div className="onboarding-mg-platform-description ">{infoLabels.platformPreferencesTitle}</div>
        </div>
        <Controller
          control={control}
          name="primaryPlatform"
          defaultValue={primaryPlatform || options[0]}
          rules={{ required: infoLabels.primaryPlatformRequired }}
          render={({ field, fieldState: { error } }) => (
            <Select
              id="primary-platform"
              selectedOption={options.find((item) => item.value === primaryPlatform?.value) ?? undefined}
              errorMessage={error && error.message}
              onChange={(item) => onPrimaryCardChange(item as PlatformOption, field)}
              options={options}
              label={pageLabels.primaryPlatform}
            />
          )}
        />
        <Card>
          <img alt="Platform image" src={platformIcon || "/img/default-platform.png"} className="image-as-icon" />
        </Card>
      </div>
      <div className="onboarding-mg-sc-platform-container">
        <div className="mg-intro">
          <h4 className="onboarding-mg-secondary-title" data-disabled={secondaryProps.disabled}>
            {infoLabels.secondaryPlatforms}
          </h4>
          <div className="onboarding-mg-platform-description" data-disabled={secondaryProps.disabled}>
            {infoLabels.secondaryPlatformsTitle}
          </div>
        </div>
        {secondaryProps.items && (
          <Controller
            control={control}
            name={secondaryProps.name}
            defaultValue={secondaryPlatforms}
            render={({ field }) => (
              <CheckboxCards {...field} items={secondaryProps.items} disabled={secondaryProps.disabled} />
            )}
          />
        )}
      </div>
    </>
  );
});
