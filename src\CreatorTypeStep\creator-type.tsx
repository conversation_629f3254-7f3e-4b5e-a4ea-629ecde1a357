import React, { FC, ReactElement } from "react";
import { NextRouter } from "next/router";
import { BrowserAnalytics, Close<PERSON><PERSON><PERSON>, Dispatch, ErrorHandling, OnboardingStepConfiguration, State } from "../types";
import CreatorTypePage from "./CreatorTypePage/CreatorTypePage";
import { CreatorTypeFormLabels } from "./CreatorTypeForm/CreatorTypeForm";
import { Url } from "next/dist/shared/lib/router/router";

export type PageLabels = {
  creatorTitle: string;
  creatorDescription: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  unhandledError: string;
};

export type FormLabels = {
  cancel: string;
  next: string;
  yes: string;
  no: string;
  close: string;
  save: string;
  discard: string;
};

export type CreatorType = {
  imageAsIcon: string;
  value: string;
  label: string;
};

export type CreatorTypeProps = {
  analytics: BrowserAnalytics;
  state: State;
  stableDispatch: Dispatch;
  dispatch: Dispatch;
  configuration: OnboardingStepConfiguration;
  errorHandling: ErrorHandling;
  labels: Labels;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  setShowMigration: (value: boolean) => void;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  locale: string;
  showMigration: boolean;
  router: NextRouter;
  navigateToPage: Url;
  PROGRAM_CODE: string;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  basePath?: string;
  onContinueToNextPage?: () => void;
};

export type Labels = FormLabels & PageLabels & CreatorTypeFormLabels;

const CreatorType: FC<CreatorTypeProps> = ({
  setShowMigration,
  state,
  stableDispatch,
  configuration,
  labels,
  analytics,
  errorHandling,
  dispatch,
  onClose,
  showConfirmation,
  setShowConfirmation,
  errorToast,
  showMigration,
  breadcrumbLabels,
  router,
  locale,
  navigateToPage,
  PROGRAM_CODE,
  basePath,
  onContinueToNextPage
}) => {
  return (
    <div className="onboarding-creator">
      <CreatorTypePage
        formLabels={labels}
        pageLabels={labels}
        onClose={onClose}
        showConfirmation={showConfirmation}
        dispatch={dispatch}
        stableDispatch={stableDispatch}
        errorToast={errorToast}
        unhandledError={labels?.unhandledError}
        state={state}
        setShowConfirmation={setShowConfirmation}
        router={router}
        locale={locale}
        analytics={analytics}
        setShowMigration={setShowMigration}
        showMigration={showMigration}
        errorHandling={errorHandling}
        configuration={configuration}
        breadcrumbLabels={breadcrumbLabels}
        navigateToPage={navigateToPage}
        PROGRAM_CODE={PROGRAM_CODE}
        basePath={basePath}
        onContinueToNextPage={onContinueToNextPage}
      />
    </div>
  );
};

export default CreatorType;
