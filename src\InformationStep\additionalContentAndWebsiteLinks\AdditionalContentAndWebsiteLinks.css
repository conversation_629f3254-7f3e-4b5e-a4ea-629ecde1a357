.onboarding-mg-content-url-add-more-text {
  @apply ml-[6px] self-center font-display-bold xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10 leading-4 tracking-[1px];
}

.onboarding-mg-content-url-icon {
  @apply h-[18px] text-gray-10;
}

.onboarding-mg-information-additional-content-urls {
  @apply flex w-full flex-col self-start;
}

.onboarding-mg-information-additional-content-url {
  @apply mb-meas0 flex w-full;
}

.onboarding-mg-information-additional-content-url:nth-child(n + 2) {
  @apply mt-meas10;
}

.onboarding-mg-information-additional-content-url > label:last-of-type {
  @apply col-span-2 w-full flex-[1] md:flex-[0.9];
}

.onboarding-mg-information-additional-content-url-without-delete > label:last-of-type {
  @apply md:flex-1;
}

.onboarding-mg-information-additional-content-add-more {
  @apply flex w-fit cursor-pointer items-center;
}

.onboarding-mg-information-additional-content-delete {
  @apply mt-meas10 flex h-meas20 flex-[0.2] items-center justify-center md:flex-[0.1];
}

.onboarding-mg-information-additional-content-container {
  @apply mb-meas16 mt-meas16 flex w-full flex-col justify-between border-t border-white border-opacity-[0.33] pt-meas16 md:w-[672px];
}
.onboarding-mg-information-additional-content {
  @apply flex w-full flex-col gap-meas10;
}
.onboarding-mg-information-additional-content .content-in-center {
  @apply flex w-[91.666667%] flex-col items-center md:w-[640px] xl:w-[790px];
}
.onboarding-mg-information-additional-content-title {
  @apply font-display-bold text-[1.5rem] leading-8 tracking-[1px] text-white xs:text-mobile-h4 md:text-center md:text-tablet-h4 lg:text-desktop-h4;
}
.onboarding-mg-information-additional-content-description {
  @apply text-center font-text-regular text-[1rem] font-normal leading-6;
}
