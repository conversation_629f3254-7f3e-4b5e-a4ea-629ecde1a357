import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { useRouter } from "next/router";
import MarketplaceDisplayNameStep from "./MarketplaceDisplayNameStep";

jest.mock("next/router");

describe("MarketplaceDisplayNameStep", () => {
  const defaultPageLabels = {
    title: "Create Display Name",
    description: "Choose a unique display name for the marketplace",
    nameRequirements: "Name must be 22 characters or less and contain only letters and numbers",
    publicDisplayName: "Create Display Name",
    confirmPublicDisplayName: "Confirm Display Name",
    placeholder: "Enter your display name",
    understandingCheckbox: "I understand that this name will be publicly visible",
    next: "Next",
    cancel: "Cancel",
    displayNote: "This name is displayed on the marketplace to the public."
  };
  const defaultInfoLabels = {
    publicDisplayNameRequired: "Display name is required",
    confirmNameRequired: "Please confirm your display name",
    tooLong: "Name must be 22 characters or less",
    specialCharactersNotAllowed: "Only letters and numbers are allowed",
    doNotMatch: "Display names do not match",
    understandingRequired: "You must acknowledge the terms",
    alreadyInUse: "This name is already taken",
    inappropriateLanguage: "This name contains inappropriate language"
  };
  const marketDisplayNameLabels = {
    pageLabels: defaultPageLabels,
    infoLabels: defaultInfoLabels
  };
  const marketDisplayNameInputsProps = {
    labels: marketDisplayNameLabels,
    dispatch: jest.fn(),
    errorHandling: jest.fn(),
    router: useRouter(),
    onClose: jest.fn(),
    onStepNavigation: jest.fn(),
    backgroundImage: "/icons/info-union.svg",
    validationError: ""
  };
  const {
    labels: { pageLabels, infoLabels }
  } = marketDisplayNameInputsProps;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Existing tests...
  it("shows validation errors for required fields", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const createNameInput = screen.getByLabelText(pageLabels.publicDisplayName);
    await userEvent.type(createNameInput, "test");
    await userEvent.clear(createNameInput);
    await userEvent.tab();

    expect(await screen.findByText(infoLabels.publicDisplayNameRequired)).toBeInTheDocument();

    const confirmNameInput = screen.getByLabelText(pageLabels.confirmPublicDisplayName);
    await userEvent.type(confirmNameInput, "test");
    await userEvent.clear(confirmNameInput);
    await userEvent.tab();

    expect(await screen.findByText(infoLabels.confirmNameRequired)).toBeInTheDocument();

    await userEvent.click(screen.getByRole("checkbox"));
    await userEvent.click(screen.getByRole("checkbox"));

    expect(await screen.findByText(infoLabels.understandingRequired)).toBeInTheDocument();
  });

  it("navigates to next page after submitting the display name information", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    const submitButton = screen.getByRole("button", { name: pageLabels.next });
    expect(submitButton).toBeDisabled();
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "ValidName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "ValidName");
    await userEvent.click(screen.getByRole("checkbox"));
    await waitFor(() => {
      expect(submitButton).toBeEnabled();
    });

    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(marketDisplayNameInputsProps.onStepNavigation).toHaveBeenCalled();
    });
  });

  it("shows validation error when names do not match", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "NameOne");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "NameTwo");

    await userEvent.tab();

    expect(await screen.findByText(infoLabels.doNotMatch)).toBeInTheDocument();
  });

  it("cancels saving marketplace display name", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    const cancelButton = screen.getByRole("button", { name: pageLabels.cancel });

    await userEvent.click(cancelButton);

    expect(marketDisplayNameInputsProps.onClose).toHaveBeenCalled();
  });

  it("is accessible", async () => {
    const { container } = render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it("highlights display names as valid when they match", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "MyValidName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "MyValidName");

    await waitFor(() => {
      const inputContainers = screen.getAllByTestId("input-box");

      expect(inputContainers[0]).toHaveClass("input-box-validated");
      expect(inputContainers[1]).toHaveClass("input-box-validated");
    });
  });

  it("calls error handler on form submission errors", async () => {
    const error = new Error("server validation error");
    const errorHandling = jest.fn();
    const dispatch = jest.fn();
    render(
      <MarketplaceDisplayNameStep
        {...marketDisplayNameInputsProps}
        errorHandling={errorHandling}
        dispatch={dispatch}
        onStepNavigation={() => {
          throw error;
        }}
      />
    );
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "TakenName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "TakenName");
    await userEvent.click(screen.getByRole("checkbox"));

    await userEvent.click(screen.getByRole("button", { name: pageLabels.next }));

    await waitFor(() => {
      expect(errorHandling).toHaveBeenCalledTimes(1);
      const [firstArg, secondArg] = errorHandling.mock.calls[0];
      expect(firstArg).toBe(dispatch);
      expect(secondArg).toBeInstanceOf(Error);
      expect(secondArg.message).toBe("server validation error");
    });
  });

  it("shows error message when names don’t match", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "FirstName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "SecondName");
    await userEvent.click(screen.getByRole("checkbox"));

    await userEvent.click(screen.getByRole("button", { name: pageLabels.next }));

    expect(await screen.findByText(infoLabels.doNotMatch)).toBeInTheDocument();
  });

  it("shows validation error for names exceeding maximum length", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const longName = "a".repeat(23); // Exceeds 22 character limit
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), longName);

    expect(await screen.findByText(infoLabels.tooLong)).toBeInTheDocument();
  });

  it("shows validation error for special characters in public display name", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "Test@Name!");

    expect(await screen.findByText(infoLabels.specialCharactersNotAllowed)).toBeInTheDocument();
  });

  it("shows validation error for special characters in confirm display name", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "Test@Name!");

    expect(await screen.findByText(infoLabels.specialCharactersNotAllowed)).toBeInTheDocument();
  });

  it("clears confirm name validation error when names are corrected to match", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    const publicNameInput = screen.getByLabelText(pageLabels.publicDisplayName);
    const confirmNameInput = screen.getByLabelText(pageLabels.confirmPublicDisplayName);
    // Create mismatch first
    await userEvent.type(publicNameInput, "TestName1");
    await userEvent.type(confirmNameInput, "TestName2");
    expect(await screen.findByText(infoLabels.doNotMatch)).toBeInTheDocument();

    // Fix the mismatch
    await userEvent.clear(confirmNameInput);
    await userEvent.type(confirmNameInput, "TestName1");

    await waitFor(() => {
      expect(screen.queryByText(infoLabels.doNotMatch)).not.toBeInTheDocument();
    });
  });

  it("disables submit button when public display name is missing", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const submitButton = screen.getByRole("button", { name: pageLabels.next });

    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "ValidName");
    await userEvent.click(screen.getByRole("checkbox"));

    expect(submitButton).toBeDisabled();
  });

  it("disables submit button when confirm display name is missing", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const submitButton = screen.getByRole("button", { name: pageLabels.next });

    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "ValidName");
    await userEvent.click(screen.getByRole("checkbox"));

    expect(submitButton).toBeDisabled();
  });

  it("disables submit button when checkbox is not checked", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const submitButton = screen.getByRole("button", { name: pageLabels.next });

    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "ValidName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "ValidName");

    expect(submitButton).toBeDisabled();
  });

  it("handles checkbox toggle correctly", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const checkbox = screen.getByRole("checkbox");

    // Check
    await userEvent.click(checkbox);
    expect(checkbox).toBeChecked();

    // Uncheck
    await userEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();

    // Check again
    await userEvent.click(checkbox);
    expect(checkbox).toBeChecked();
  });

  it("disables submit button if display and confirmation names are blank", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);
    const submitButton = screen.getByRole("button", { name: pageLabels.next });

    // Test with whitespace only
    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "   ");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "   ");
    await userEvent.click(screen.getByRole("checkbox"));

    expect(submitButton).toBeDisabled();
  });

  it("validation of name and confirm fields is independent from input order", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const confirmNameInput = screen.getByLabelText(pageLabels.confirmPublicDisplayName);
    const publicNameInput = screen.getByLabelText(pageLabels.publicDisplayName);

    // Type in confirm field first
    await userEvent.type(confirmNameInput, "TestName");

    // Then type matching name in public field
    await userEvent.type(publicNameInput, "TestName");

    // Should clear any previous errors
    await waitFor(() => {
      expect(screen.queryByText(infoLabels.doNotMatch)).not.toBeInTheDocument();
    });
  });

  it("handles form submission with valid data correctly", async () => {
    const onStepNavigation = jest.fn().mockResolvedValue(true);

    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} onStepNavigation={onStepNavigation} />);

    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "ValidName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "ValidName");
    await userEvent.click(screen.getByRole("checkbox"));

    const submitButton = screen.getByRole("button", { name: pageLabels.next });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(onStepNavigation).toHaveBeenCalledTimes(1);
    });
  });

  it("handles all dependency array values correctly", async () => {
    const dispatch = jest.fn();
    const mockErrorHandling = jest.fn();
    const mockOnStepNavigation = jest.fn();

    render(
      <MarketplaceDisplayNameStep
        {...marketDisplayNameInputsProps}
        dispatch={dispatch}
        errorHandling={mockErrorHandling}
        onStepNavigation={mockOnStepNavigation}
      />
    );

    await userEvent.type(screen.getByLabelText(pageLabels.publicDisplayName), "ValidName");
    await userEvent.type(screen.getByLabelText(pageLabels.confirmPublicDisplayName), "ValidName");
    await userEvent.click(screen.getByRole("checkbox"));

    const submitButton = screen.getByRole("button", { name: pageLabels.next });
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnStepNavigation).toHaveBeenCalled();
    });
  });

  it("renders all required UI elements", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    // Check for title (using getAllByText due to multiple elements with same text)
    expect(screen.getAllByText(pageLabels.title)).toHaveLength(2); // H3 and label
    expect(screen.getByText(pageLabels.description)).toBeInTheDocument();
    expect(screen.getByText(pageLabels.nameRequirements)).toBeInTheDocument();
    expect(screen.getByLabelText(pageLabels.publicDisplayName)).toBeInTheDocument();
    expect(screen.getByLabelText(pageLabels.confirmPublicDisplayName)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: pageLabels.next })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: pageLabels.cancel })).toBeInTheDocument();
    expect(screen.getByText(pageLabels.displayNote)).toBeInTheDocument();
  });

  it("handles form validation with both true and false conditions", async () => {
    render(<MarketplaceDisplayNameStep {...marketDisplayNameInputsProps} />);

    const publicNameInput = screen.getByLabelText(pageLabels.publicDisplayName);
    const confirmNameInput = screen.getByLabelText(pageLabels.confirmPublicDisplayName);
    const checkbox = screen.getByRole("checkbox");
    const submitButton = screen.getByRole("button", { name: pageLabels.next });

    // Test all false conditions
    expect(submitButton).toBeDisabled();

    // Test partial completion
    await userEvent.type(publicNameInput, "ValidName");
    expect(submitButton).toBeDisabled();

    await userEvent.type(confirmNameInput, "ValidName");
    expect(submitButton).toBeDisabled();

    // Test all true conditions
    await userEvent.click(checkbox);
    await waitFor(() => {
      expect(submitButton).toBeEnabled();
    });
  });
});
