import { Checkbox, Input } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo, useCallback, useMemo } from "react";
import { Controller, FieldValues, Message, useFormContext, ValidationRule } from "react-hook-form";
import Footer from "../common/Footer/Footer";
import Form from "../utils/Form";
import { Dispatch, ErrorHandling } from "../types";
import { useAsync } from "../utils";

export type MarketDisplayNamePageLabels = {
  title: string;
  description: string;
  nameRequirements: string;
  publicDisplayName: string;
  confirmPublicDisplayName: string;
  placeholder: string;
  understandingCheckbox: string;
  next: string;
  cancel: string;
  displayNote: string;
};

export type MessageInfoLabels = {
  publicDisplayNameRequired: string;
  confirmNameRequired: string;
  tooLong: string;
  specialCharactersNotAllowed: string;
  doNotMatch: string;
  understandingRequired: string;
  alreadyInUse: string;
  inappropriateLanguage: string;
};

export type MarketDisplayNameLabels = {
  pageLabels: MarketDisplayNamePageLabels;
  infoLabels: MessageInfoLabels;
};

export type MarketDisplayNameInputsProps = {
  labels: MarketDisplayNameLabels;
  isPending: boolean;
  onClose: () => void;
  backgroundImage: string;
};

type Pattern = {
  value: RegExp;
  message: string;
};

type MaxLength = {
  value: number;
  message: string;
};

type MaxLengthPatternRequired = {
  required: Message | ValidationRule<boolean>;
  maxLength: MaxLength;
  pattern: Pattern;
};

export type MarketplaceDisplayNameStepRules = {
  publicDisplayName: MaxLengthPatternRequired;
  confirmPublicDisplayName: MaxLengthPatternRequired;
  understandingCheckbox: {
    required: Message | ValidationRule<boolean>;
  };
};

const MarketplaceDisplayNameInputs = memo(function MarketplaceDisplayNameInputs({
  labels: { pageLabels, infoLabels },
  isPending,
  onClose,
  backgroundImage
}: MarketDisplayNameInputsProps) {
  const methods = useFormContext();
  const { control, formState, getValues, setError, clearErrors, watch } = methods;

  const watchedValues = watch();

  const isFormComplete = useMemo(() => {
    const publicDisplayName = watchedValues?.publicDisplayName?.trim();
    const confirmPublicDisplayName = watchedValues?.confirmPublicDisplayName?.trim();
    const understandingCheckbox = watchedValues?.understandingCheckbox;

    return !!(
      publicDisplayName &&
      confirmPublicDisplayName &&
      publicDisplayName === confirmPublicDisplayName &&
      understandingCheckbox &&
      understandingCheckbox.length > 0
    );
  }, [watchedValues]);

  const hasErrors = Object.keys(formState.errors).length !== 0;
  const disableSubmit = hasErrors || isPending || !isFormComplete;

  const rules: MarketplaceDisplayNameStepRules = {
    publicDisplayName: {
      required: infoLabels.publicDisplayNameRequired,
      maxLength: {
        value: 22,
        message: infoLabels.tooLong
      },
      pattern: {
        value: /^[A-Za-z0-9]*$/,
        message: infoLabels.specialCharactersNotAllowed
      }
    },
    confirmPublicDisplayName: {
      required: infoLabels.confirmNameRequired,
      maxLength: {
        value: 22,
        message: infoLabels.tooLong
      },
      pattern: {
        value: /^[A-Za-z0-9]*$/,
        message: infoLabels.specialCharactersNotAllowed
      }
    },
    understandingCheckbox: {
      required: infoLabels.understandingRequired
    }
  };

  const compareTwoNames = (value: string) => {
    const confirmPublicDisplayName = getValues("confirmPublicDisplayName");
    if (value && confirmPublicDisplayName) {
      clearErrors([`confirmPublicDisplayName`]);
      if (confirmPublicDisplayName !== value) {
        setError(`confirmPublicDisplayName`, { type: "manual", message: infoLabels.doNotMatch });
      }
    }
  };

  const buttons = useMemo(
    () => ({
      cancel: pageLabels?.cancel,
      next: pageLabels?.next
    }),
    [pageLabels]
  );

  const getFieldValidation = (fieldName: string) => {
    const createValue = getValues("publicDisplayName");
    const confirmValue = getValues("confirmPublicDisplayName");
    const fieldError = formState.errors[fieldName];
    return !fieldError && createValue && confirmValue && createValue === confirmValue;
  };

  return (
    <>
      <div className="marketplace-display-name-form-container">
        <div className="marketplace-display-name-form-information">
          <h3 className="marketplace-display-name-form-title">{pageLabels.title}</h3>
          <div className="marketplace-display-name-form-description">{pageLabels.description}</div>
        </div>
        <div className="marketplace-display-name-form-validation-info-container">
          <img src={backgroundImage} alt="" />
          <span className="marketplace-display-name-form-validation-info-label">{pageLabels.nameRequirements}</span>
        </div>
        <div className="marketplace-display-name-form-input-container">
          <div className="marketplace-display-name-input-wrapper">
            <Controller
              control={control}
              name="publicDisplayName"
              rules={{
                ...rules?.publicDisplayName,
                validate: (value) => {
                  compareTwoNames(value);
                  return true;
                }
              }}
              render={({ field, fieldState: { error } }) => (
                <>
                  <div className="marketplace-display-name-label-container">
                    <label htmlFor="publicDisplayName" className="marketplace-display-name-input-label">
                      {pageLabels.publicDisplayName}
                    </label>
                    <span className="marketplace-display-name-note">{pageLabels.displayNote}</span>
                  </div>
                  <Input
                    id="publicDisplayName"
                    errorMessage={error?.message || ""}
                    {...field}
                    placeholder={pageLabels.placeholder}
                    dark
                    validated={getFieldValidation("publicDisplayName")}
                  />
                </>
              )}
            />
          </div>
        </div>
        <div className="marketplace-display-name-form-input-container">
          <Controller
            control={control}
            name="confirmPublicDisplayName"
            rules={{
              ...rules?.confirmPublicDisplayName,
              validate: (value) => {
                return getValues("publicDisplayName")
                  ? getValues("publicDisplayName") === value || infoLabels.doNotMatch
                  : false;
              }
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="confirmPublicDisplayName"
                errorMessage={error?.message || ""}
                {...field}
                label={pageLabels.confirmPublicDisplayName}
                placeholder={pageLabels.placeholder}
                dark
                validated={getFieldValidation("confirmPublicDisplayName")}
              />
            )}
          />
        </div>
        <div className="marketplace-display-name-form-checkbox-container">
          <Controller
            control={control}
            name="understandingCheckbox"
            rules={rules?.understandingCheckbox}
            render={({ field, fieldState: { error } }) => (
              <Checkbox
                options={[
                  {
                    id: "understood",
                    value: "understood",
                    label: pageLabels.understandingCheckbox,
                    isChecked: field.value && field.value.includes("understood"),
                    onChange: (e) => {
                      const isChecked = e.target.checked;
                      if (isChecked) {
                        field.onChange(["understood"]);
                      } else {
                        field.onChange([]);
                      }
                    }
                  }
                ]}
                errorMessage={error?.message || ""}
              />
            )}
          />
        </div>
      </div>
      <Footer
        {...{
          buttons,
          onCancel: onClose,
          disableSubmit: disableSubmit,
          isPending,
          isError: hasErrors && formState.isDirty
        }}
      />
    </>
  );
});

export type MarketplaceDisplayNameStepProps = {
  labels: MarketDisplayNameLabels;
  dispatch: Dispatch;
  errorHandling: ErrorHandling;
  onClose: () => void;
  backgroundImage: string;
  onStepNavigation: () => void;
};

export default memo(function MarketplaceDisplayNameStep({
  labels: { pageLabels, infoLabels },
  dispatch,
  errorHandling,
  onClose,
  backgroundImage,
  onStepNavigation
}: MarketplaceDisplayNameStepProps) {
  const onSubmit = useCallback(
    async (_values: FieldValues): Promise<unknown> => {
      try {
        onStepNavigation();
        return Promise.resolve(true);
      } catch (e) {
        errorHandling(dispatch, e as Error);
        return Promise.reject(e);
      }
    },
    [dispatch, errorHandling]
  );

  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);

  return (
    <>
      <Form mode="onChange" onSubmit={submitHandler}>
        <MarketplaceDisplayNameInputs
          {...{
            labels: { pageLabels, infoLabels },
            onClose,
            isPending,
            backgroundImage
          }}
        />
      </Form>
    </>
  );
});
