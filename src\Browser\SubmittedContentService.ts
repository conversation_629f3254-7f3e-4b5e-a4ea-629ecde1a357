import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { ContentScanResult } from "../types";

export default class SubmittedContentService {
  constructor(private readonly client: TraceableHttpClient) {}

  async validateContent(url: { urls: string[] }, type: string): Promise<ContentScanResult> {
    const response = await this.client.post("/api/secure-content", {
      body: url,
      query: { type }
    });
    return response.data;
  }
}
