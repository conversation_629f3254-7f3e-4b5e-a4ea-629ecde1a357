import AccountInformationWithPayableInfo, {
  AccountInformationWithPayableInfoResponse
} from "./AccountInformationWithPayableStatus";

export type AccountInformationWithCreatorCodeResponse = AccountInformationWithPayableInfoResponse;

export default class AccountInformationWithCreatorCode extends AccountInformationWithPayableInfo {
  static fromApi(data: AccountInformationWithCreatorCodeResponse): AccountInformationWithCreatorCode {
    return new AccountInformationWithCreatorCode(
      data.defaultGamerTag,
      data.nucleusId,
      data.firstName,
      data.lastName,
      data.originEmail,
      data.dateOfBirth,
      data.needsMigration,
      data.status,
      data.registrationDate,
      data.isPayable,
      data.creatorCode,
      data.preferredName,
      data.preferredPronouns
    );
  }

  constructor(
    readonly defaultGamerTag: string,
    readonly nucleusId: number,
    readonly firstName: string,
    readonly lastName: string,
    readonly originEmail: string,
    readonly dateOfBirth: number,
    readonly needsMigration: boolean,
    readonly status: string,
    readonly registrationDate: number,
    readonly isPayable: boolean,
    readonly creatorCode: string,
    readonly preferredName: string,
    readonly preferredPronouns: string
  ) {
    super(
      defaultGamerTag,
      nucleusId,
      firstName,
      lastName,
      originEmail,
      dateOfBirth,
      needsMigration,
      status,
      registrationDate,
      isPayable,
      creatorCode,
      preferredName,
      preferredPronouns
    );
  }
}
