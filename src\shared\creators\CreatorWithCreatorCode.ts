import PreferredFranchise from "../franchises/PreferredFranchise";
import MailingAddress from "./MailingAddress";
import PreferredPlatform from "../platforms/PreferredPlatform";
import CommunicationPreferences from "./CommunicationPreferences";
import AdditionalInformation from "./AdditionalInformation";
import LegalEntityInformation from "./LegalEntityInformation";
import CreatorWithPayableStatus, { CreatorWithPayableStatusResponse } from "./CreatorWithPayableStatus";
import ConnectedAccount from "../channels/ConnectedAccount";
import AccountInformationWithCreatorCode, {
  AccountInformationWithCreatorCodeResponse
} from "./AccountInformationWithCreatorCode";
import { Overwrite } from "../../types";

export type CreatorWithCreatorCodeResponse = Overwrite<
  CreatorWithPayableStatusResponse,
  {
    accountInformation: AccountInformationWithCreatorCodeResponse;
  }
>;

export default class CreatorWithCreatorCode extends CreatorWithPayableStatus {
  static fromApi(data: CreatorWithCreatorCodeResponse): CreatorWithCreatorCode {
    return new CreatorWithCreatorCode(
      data.id,
      data.avatar,
      data.creatorTypes || [],
      AccountInformationWithCreatorCode.fromApi(data.accountInformation),
      data.preferredPlatforms.map((item) => PreferredPlatform.fromApi(item)),
      data.preferredFranchises.map((item) => PreferredFranchise.fromApi(item)),
      MailingAddress.fromApi(data.mailingAddress),
      CommunicationPreferences.fromApi(data.communicationPreferences),
      data.connectedAccounts || [],
      AdditionalInformation.fromApi(data.additionalInformation),
      data.legalInformation ? LegalEntityInformation.fromApi(data.legalInformation) : undefined
    );
  }

  constructor(
    readonly id: string,
    readonly avatar: string | null,
    readonly creatorTypes: Array<string>,
    public accountInformation: AccountInformationWithCreatorCode,
    preferredPlatforms: Array<PreferredPlatform>,
    preferredFranchises: Array<PreferredFranchise>,
    readonly mailingAddress: MailingAddress,
    readonly communicationPreferences: CommunicationPreferences,
    readonly connectedAccounts: Array<ConnectedAccount>,
    readonly additionalInformation: AdditionalInformation,
    readonly legalEntity?: LegalEntityInformation
  ) {
    super(
      id,
      avatar,
      creatorTypes,
      accountInformation,
      preferredPlatforms,
      preferredFranchises,
      mailingAddress,
      communicationPreferences,
      connectedAccounts,
      additionalInformation,
      legalEntity
    );
  }
}
