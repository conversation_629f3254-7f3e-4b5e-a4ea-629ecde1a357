import { Controller, useFieldArray, useFormContext } from "react-hook-form";
import React, { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState } from "react";
import { Icon, Input, outlineAdd, remove as removeIcon } from "@eait-playerexp-cn/core-ui-kit";
import cx from "classnames";
import CreatorForm from "../../utils/CreatorForm";
import { Labels } from "../../InformationStep/OnboardingInformationStep";
import { ContentUrl } from "../../types";
import SubmittedContentService from "../../Browser/SubmittedContentService";
import { ErrorResponse } from "@src/utils";

export type OnBoardingFormLabels = {
  labels: {
    additionalContentAndWebsiteTitle: string;
    additionalContentAndWebsiteLinks: string;
    additionalLinkPlaceholder: string;
    websiteUrlLabel: string;
  };
  addMoreUrlLabel: string;
  remove: string;
  messages: {
    duplicateUrl: string;
  };
};

export type AdditionalContentAndWebsiteLinksProps = {
  labels: OnBoardingFormLabels & Labels;
  socialLinks: string[] | [];
  submittedContentService?: SubmittedContentService;
};

export type AdditionalContentAndWebsiteLinksRef = {
  validateAdditionalLinks: (contentUrls: ContentUrl[]) => Promise<string[] | []>;
  hasValidationErrors: () => boolean;
};

const AdditionalContentAndWebsiteLinks = memo(
  forwardRef<AdditionalContentAndWebsiteLinksRef, AdditionalContentAndWebsiteLinksProps>(
    function AdditionalContentAndWebsiteLinks({ labels, socialLinks, submittedContentService }, ref) {
      const methods = useFormContext();
      const { infoLabels, formLabels } = labels;
      const rules = CreatorForm.rules(infoLabels);
      const { control, setError, trigger, watch, formState, clearErrors } = methods;
      const [scanResults, setScanResults] = useState<Array<{ isSecure: boolean }> | null>(null);
      const [invalidUrlErrors, setInvalidUrlErrors] = useState<string[] | null>(null);

      const { fields, append, update, remove } = useFieldArray({
        control,
        name: "contentUrls"
      });

      const validateAdditionalLinks = useCallback(
        async (contentUrls: ContentUrl[]): Promise<string[] | []> => {
          const urls: string[] = [];
          const allEmpty = contentUrls.every(({ url }: { url: string }) => !url || url === "https://" || url.trim() === "");
          // Filter out empty URLs, "https://" placeholders, and whitespace-only URLs
          contentUrls.forEach(({ url }: { url: string }) => {
            if (url && url !== "https://" && url.trim() !== "") {
              urls.push(url.trim());
            }
          });

          if (urls?.length > 0 && !allEmpty && submittedContentService) {
            try {
              const contentScanResult = await submittedContentService.validateContent({ urls }, "CREATORS");
              const results = contentScanResult.results;
              const scanFailed = !!results.find((result: { isSecure: boolean }) => !result.isSecure);
              if (scanFailed) {
                setScanResults(results);
                return [];
              }
              setScanResults(null);
              return urls;
            } catch (e: unknown) {
              setScanResults(null);
              const error = e as ErrorResponse & {
                response?: {
                  status?: number;
                  data?: {
                    code?: string;
                    message?: string;
                    detail?: string;
                    errors?: Record<string, string>;
                  };
                };
              };
              if (
                error.response?.status === 422 &&
                error.response?.data?.code === "validate-content-urls-invalid-input"
              ) {
                setInvalidUrlErrors(error.response.data.errors ? Object.keys(error.response.data.errors) : null);
                return [];
              }
              return [];
            }
          } else {
            setScanResults(null);
            setInvalidUrlErrors(null);
            return urls;
          }
        },
        [submittedContentService]
      );

      const hasValidationErrors = useCallback(() => {
        const hasContentErrors = scanResults && scanResults.some((result: { isSecure: boolean }) => !result.isSecure);
        const hasUrlErrors = invalidUrlErrors && invalidUrlErrors.length > 0;
        return !!(hasContentErrors || hasUrlErrors);
      }, [scanResults, invalidUrlErrors]);

      useImperativeHandle(
        ref,
        () => ({
          validateAdditionalLinks,
          hasValidationErrors
        }),
        [validateAdditionalLinks, hasValidationErrors]
      );
      const contentUrlChanged = watch("contentUrls");
      useEffect(() => {
        if (socialLinks && socialLinks.length > 0) {
          if (fields.length > 0) {
            for (let i = fields.length - 1; i >= 0; i--) {
              remove(i);
            }
          }
          socialLinks.forEach((url: string) => {
            append({ url, saved: true, followers: "" });
          });
          trigger("contentUrls");
        } else if (socialLinks && socialLinks.length === 0 && fields.length === 0) {
          append({ url: "", saved: false, followers: "" });
          trigger("contentUrls");
        }
      }, [socialLinks, append, remove, trigger, fields.length]);

      const clearInput = useCallback(
        (index: number) => () => {
          const defaultValue = "https://";
          update(index, { ...contentUrlChanged[index], url: defaultValue, saved: false });
          trigger(`contentUrls.${index}.url`);
        },
        [update, trigger, JSON.stringify(contentUrlChanged)]
      );
      const onDelete = useCallback((index: number) => () => remove(index), [remove]);
      const onAddUrls = useCallback(() => {
        append({ url: "", saved: false, followers: "" });
        trigger(`contentUrls`);
      }, [append, trigger]);

      useEffect(() => {
        if (scanResults?.length) {
          scanResults.map(({ isSecure }, index) => {
            if (isSecure) {
              clearErrors(`contentUrls[${index}].url`);
            } else {
              setError(`contentUrls[${index}].url`, { type: "manual", message: formLabels.urlScanFailed });
            }
          });
        } else if (invalidUrlErrors?.length) {
          invalidUrlErrors.map((value) => {
            const pos = value.substring(5, 6);
            setError(`contentUrls[${pos}].url`, { type: "manual", message: formLabels.invalidUrl });
          });
        }
      }, [scanResults, setError, invalidUrlErrors]);

      useEffect(() => {
        let timer: NodeJS.Timeout;
        const urlFields = contentUrlChanged?.map(({ url }: { url: string }) => url.trim());
        // this will trigger error on duplicate index
        urlFields?.forEach((item: string, index: number) => {
          const duplicateFound = urlFields.indexOf(item) !== index;
          if (duplicateFound && item.trim() && item.trim() !== "https://") {
            if (
              (formState?.errors?.contentUrls as unknown as [{ url: { message: string } }])?.[index]?.url?.message !==
              labels?.messages.duplicateUrl
            ) {
              timer = setTimeout(() => {
                setError(`contentUrls[${index}].url`, { type: "manual", message: labels?.messages.duplicateUrl });
              });
            }
          } else {
            if (
              (formState?.errors?.contentUrls as unknown as [{ url: { message: string } }])?.[index]?.url?.message ===
              labels?.messages.duplicateUrl
            ) {
              clearErrors(`contentUrls[${index}].url`);
              trigger("contentUrls");
            }
          }
        }, []);
        return () => clearTimeout(timer);
      }, [JSON.stringify(formState.errors), labels?.messages.duplicateUrl, JSON.stringify(contentUrlChanged)]);

      return (
        <div className="onboarding-mg-information-additional-content">
          <h4 className="onboarding-mg-information-additional-content-title">
            {labels?.labels.additionalContentAndWebsiteTitle}
          </h4>
          <p className="onboarding-mg-information-additional-content-description">
            {labels?.labels.additionalContentAndWebsiteLinks}
          </p>
          <ul
            className="onboarding-mg-information-additional-content-urls"
            aria-label="onboarding-mg-information-additional-content-urls"
          >
            {fields.map((item, index) => {
              return (
                <li
                  key={item.id}
                  className={cx("onboarding-mg-information-additional-content-url", {
                    "onboarding-mg-information-additional-content-url-without-delete": index === 0
                  })}
                >
                  <Controller
                    control={control}
                    name={`contentUrls.${index}.url`}
                    key={`contentUrls.${index}.url`}
                    rules={rules?.url}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        dark
                        errorMessage={error?.message || ""}
                        id={`contentUrls.${index}.url`}
                        {...field}
                        placeholder={labels?.labels.additionalLinkPlaceholder}
                        label={labels?.labels.websiteUrlLabel}
                        clearContent
                        onClearContent={clearInput(index)}
                        type="url"
                      />
                    )}
                  />
                  {!!index && (
                    <button
                      type="button"
                      aria-label={labels?.remove}
                      className="onboarding-mg-information-additional-content-delete"
                      onClick={onDelete(index)}
                    >
                      <Icon
                        icon={removeIcon}
                        className="onboarding-mg-content-url-icon"
                        id="onboarding-mg-content-url-icon"
                      />
                    </button>
                  )}
                </li>
              );
            })}
          </ul>
          {fields.length < 10 && (
            <button type="button" className="onboarding-mg-information-additional-content-add-more" onClick={onAddUrls}>
              <Icon
                icon={outlineAdd}
                className="onboarding-mg-content-url-add-more-icon"
                id="onboarding-mg-content-url-add-more-icon"
              />
              <p className="onboarding-mg-content-url-add-more-text">{labels?.addMoreUrlLabel}</p>
            </button>
          )}
        </div>
      );
    }
  )
);

export default AdditionalContentAndWebsiteLinks;
