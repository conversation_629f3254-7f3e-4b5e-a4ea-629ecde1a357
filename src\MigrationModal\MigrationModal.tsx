import React, { memo, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  Modal<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { useFormContext } from "react-hook-form";

export type LayoutType = {
  buttons: {
    cancel: string;
    discard: string;
    save: string;
    close: string;
  };
};

type MigrationModalProps = {
  setShowMigration: (value: boolean) => void;
  showMigration: boolean;
  onSave: () => void;
  onDiscard: () => void;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  layout: LayoutType;
};

type FooterButtonsProps = {
  buttons: { cancel: string; discard: string; save: string };
  onSave: () => void;
  onDiscard: () => void;
  cancelButtonRef: React.RefObject<HTMLButtonElement>;
  handleCloseModal: () => void;
  formState: { errors: Record<string, unknown>; isValid: boolean };
};

const FooterButtons = memo(function FooterButtons({
  buttons: { cancel, discard },
  onDiscard,
  cancelButtonRef,
  handleCloseModal
}: FooterButtonsProps) {
  return (
    <>
      <Button variant="tertiary" size="sm" dark onClick={handleCloseModal} ref={cancelButtonRef}>
        {cancel}
      </Button>
      <Button
        variant="secondary"
        dark
        onClick={() => {
          handleCloseModal();
          onDiscard();
        }}
      >
        {discard}
      </Button>
    </>
  );
});

export default function MigrationModal({
  setShowMigration,
  showMigration,
  onSave,
  onDiscard,
  breadcrumbLabels,
  layout
}: MigrationModalProps): JSX.Element {
  const methods = useFormContext();
  const { formState } = methods;
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const handleCloseModal = useCallback(() => setShowMigration(!showMigration), [showMigration]);
  const cancelButtonRef = useRef(null);

  return formState.isDirty && showMigration && formModified ? (
    <ModalV2 closeButtonRef={cancelButtonRef}>
      <ModalHeader>
        <ModalTitle>{breadcrumbLabels.modalTitle}</ModalTitle>
        <ModalCloseButton ariaLabel={layout.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
      </ModalHeader>
      <ModalBody>
        <p>{breadcrumbLabels.modalMessage}</p>
      </ModalBody>
      <ModalFooter showDivider>
        <FooterButtons
          {...{ buttons: layout.buttons, onSave, onDiscard, cancelButtonRef, handleCloseModal, formState }}
        />
      </ModalFooter>
    </ModalV2>
  ) : (
    <></>
  );
}
