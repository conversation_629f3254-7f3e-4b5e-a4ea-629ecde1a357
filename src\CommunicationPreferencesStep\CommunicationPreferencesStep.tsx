import React, { FC, ReactElement, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Toast } from "@eait-playerexp-cn/core-ui-kit";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { Language, Locale } from "@eait-playerexp-cn/metadata-types";
import { NextRouter } from "next/router";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  LOADING,
  onToastClose,
  SESSION_USER,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import { BrowserAnalytics, CloseHandler, Dispatch, ErrorHandling, OnboardingStepConfiguration, State } from "../types";
import Form from "../utils/Form";
import CancelRegistrationModal from "../common/CancelRegistrationModal/CancelRegistrationModal";
import { FieldValues, useFormContext, ValidationRule } from "react-hook-form";
import MigrationModal from "../MigrationModal/MigrationModal";
import Footer from "../common/Footer/Footer";
import CommunicationPreferences from "../shared/creators/CommunicationPreferences";
import CommunicationPreferencesInput from "./CommunicationPreferencesInput/CommunicationPreferencesInput";
import { ConnectAccounts } from "./ConnectedAccount/ConnectedAccounts";
import { User } from "@eait-playerexp-cn/server-kernel";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import CreatorService from "../Browser/CreatorService";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import PreferredLanguage from "@src/shared/languages/PreferredLanguage";

export type ConnectAccountLabels = {
  title: string;
  removeAccountDescription1: string;
  removeAccountDescription2: string;
  remove: string;
  cancel: string;
  myAccount: string;
  removeAccount: string;
  expireAccount: string;
  or: string;
  reconnectAccount: string;
  close: string;
  addAccount: string;
  connectAnAccount: string;
  modal: {
    removeAccountTitle: string;
    removeAccountDescription1: string;
    removeAccountDescription2: string;
  };
};

export type Translation = {
  messages: {
    preferredEmailTooLong: string;
    preferredEmailInvalid: string;
    preferredPhoneNumber: string | ValidationRule<boolean>;
    preferredPhoneNumberTooLong: string;
    contentLanguage: string | ValidationRule<boolean>;
    language: string | ValidationRule<boolean>;
    preferredEmail: string;
  };
  labels: {
    preferredEmail: string;
    preferredEmailAddressTitle: string;
    preferredEmailAddressDescription: string;
    preferredPhoneNumberTitle: string;
    preferredPhoneNumberDescription: string;
    preferredPhoneNumber: string;
    contentLanguageTitle: string;
    contentLanguagesDescription: string;
    contentLanguage: string;
    languageTitle: string;
    language: string;
    languageDescription: string;
    contentLanguagesTitle: string;
    discordTitle: string;
    discordDescription: string;
    addDiscord: string;
  };
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  title: string;
  description: string;
};

export type Layout = {
  main: { unhandledError: string };
  buttons: { yes: string; no: string; close: string; cancel: string; discard: string; save: string; next: string };
};
export type Labels = {
  layout: Layout;
  translation: Translation;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
} & ConnectAccountLabels;

export type CommunicationPreferencesFormProps = {
  translation: Translation;
  languages: Array<Language>;
  locales: Array<Locale> | null;
  communications: CommunicationPreferences | null;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  labels: Labels;
  layout: Layout;
  removeAccount: boolean | null;
  setRemoveAccount: (account: boolean) => void;
  accountToRemove: string | boolean;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  onClose: () => void;
  pending: boolean;
  showMigration: boolean;
  setShowMigration: (showMigration: boolean) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submitHandle: (data: any, navigateBack: boolean, navigateToPage: string) => void;
  stableDispatch: Dispatch;
  router: NextRouter;
  navigateToPage: string;
  userNavigated: boolean;
  errorHandling: ErrorHandling;
  state: State;
  configuration: OnboardingStepConfiguration;
  connectAccounts: ConnectAccounts;
};

const CommunicationPreferencesForm: FC<CommunicationPreferencesFormProps> = ({
  languages,
  locales,
  communications,
  showAddConfirmation,
  setShowAddConfirmation,
  labels,
  layout,
  removeAccount,
  setRemoveAccount,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  onClose,
  pending,
  showMigration,
  setShowMigration,
  submitHandle,
  stableDispatch,
  router,
  navigateToPage,
  userNavigated,
  errorHandling,
  state,
  configuration,
  connectAccounts
}) => {
  const { getValues, formState, trigger } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!formState.isDirty, [formState]);

  const onSave = () => submitHandle(data, true, navigateToPage);

  const onDiscard = () => {
    if (stableDispatch) stableDispatch({ type: USER_NAVIGATED, data: false });
    if (navigateToPage) router.push(navigateToPage);
    else router.push("/connect-accounts");
  };

  useEffect(() => {
    if (communications) {
      // Trigger validation on all fields without user interaction
      trigger(undefined, { shouldFocus: false });
    }
  }, [communications, trigger]);

  useEffect(() => {
    if (userNavigated && !formModified) {
      if (navigateToPage) router.push(navigateToPage);
      else router.push("/connect-accounts");
      if (stableDispatch) stableDispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  return (
    <>
      <CommunicationPreferencesInput
        languages={languages}
        locales={locales as Array<Locale>}
        communications={communications as CommunicationPreferences}
        showAddConfirmation={showAddConfirmation}
        setShowAddConfirmation={setShowAddConfirmation}
        labels={labels}
        removeAccount={removeAccount}
        setRemoveAccount={setRemoveAccount}
        accountToRemove={accountToRemove}
        setAccountToRemove={setAccountToRemove}
        showRemoveAccountModal={showRemoveAccountModal}
        setShowRemoveAccountModal={setShowRemoveAccountModal}
        stableDispatch={stableDispatch}
        errorHandling={errorHandling}
        state={state}
        configuration={configuration}
        connectAccounts={connectAccounts}
      />
      <Footer buttons={layout.buttons} onCancel={onClose} disableSubmit={pending} isPending={pending} />
      <MigrationModal
        setShowMigration={setShowMigration}
        showMigration={showMigration}
        onSave={onSave}
        onDiscard={onDiscard}
        breadcrumbLabels={labels.breadcrumbLabels}
        layout={layout}
      />
    </>
  );
};

export function useIsMounted(): () => boolean {
  const isMounted = useRef<boolean>(false);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  return useCallback(() => isMounted.current, []);
}

type CommunicationPreferencesStepConfiguration = {
  programCode: string;
} & OnboardingStepConfiguration;

export type CommunicationPreferencesStepProps = {
  user: User;
  analytics: BrowserAnalytics;
  configuration: CommunicationPreferencesStepConfiguration;
  errorHandling: ErrorHandling;
  router: NextRouter;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  stableDispatch: Dispatch;
  state: State;
  labels: Labels;
  navigateToPage: string;
  connectAccounts: ConnectAccounts;
  setShowConfirmation: (showConfirmation: boolean) => void;
  showConfirmation: boolean;
  showMigration: boolean;
  setShowMigration: (showMigration: boolean) => void;
};
const CommunicationPreferencesStep: FC<CommunicationPreferencesStepProps> = ({
  user,
  analytics,
  configuration,
  errorHandling,
  router,
  errorToast,
  stableDispatch,
  state,
  labels,
  navigateToPage,
  connectAccounts,
  setShowConfirmation,
  showConfirmation,
  showMigration,
  setShowMigration
}) => {
  const creatorService = configuration.creatorsClient
    ? useMemo(
        () =>
          new CreatorsService(configuration.creatorsClient as TraceableHttpClient, configuration.DEFAULT_AVATAR_IMAGE),
        [configuration.creatorsClient]
      )
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? useMemo(
        () => new CreatorService(configuration.onBoardingClient as TraceableHttpClient),
        [configuration.onBoardingClient]
      )
    : undefined;
  const metadataService = useMemo(
    () => new MetadataService(configuration.metadataClient),
    [configuration.metadataClient]
  );
  const isMounted = useIsMounted();
  const {
    popupOpened = false,
    isValidationError,
    isError,
    onboardingSteps,
    userNavigated
  } = state as {
    popupOpened: boolean;
    isValidationError: [];
    isError: boolean;
    onboardingSteps: Array<Record<string, unknown>>;
    userNavigated: boolean;
  };
  const { layout, translation } = labels;
  const {
    main: { unhandledError }
  } = layout;
  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = translation;
  const [removeAccount, setRemoveAccount] = useState<boolean | null>(null);
  const [communications, setCommunications] = useState<CommunicationPreferences | null>(null);
  const [languages, setLanguages] = useState<Array<Language>>([]);
  const [locales, setLocales] = useState<Array<Locale> | null>(null);
  const [showAddConfirmation, setShowAddConfirmation] = useState(false);
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const handleModalClose = () => setShowConfirmation(false);
  const [accountToRemove, setAccountToRemove] = useState<boolean | string>(false);
  const [showRemoveAccountModal, setShowRemoveAccountModal] = useState(false);

  const submitHandle = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async (data: any, navigateBack: boolean, navigateToPage: string) => {
      try {
        stableDispatch({ type: USER_NAVIGATED, data: false });
        const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
        const payload = {
          communicationPreferences: data,
          creatorConnectedProgram: configuration.programCode
        };
        let preferredLanguage = null;
        if (data.preferredLanguage.value && data.preferredLanguage.label) {
          preferredLanguage = { code: data.preferredLanguage.value, name: data.preferredLanguage.label };
        }
        const contentLanguages = data.contentLanguages.map((language: PreferredLanguage) => ({
          code: language.value,
          name: language.label
        }));
        const communicationPreferencesData = {
          ...data,
          contentLanguages
        };
        delete communicationPreferencesData.preferredLanguage;

        if (legacyCreatorService) await legacyCreatorService.update(payload);
        else
          await creatorService?.updateCreator({
            communicationPreferences: communicationPreferencesData,
            program: { code: configuration.programCode, preferredLanguage: preferredLanguage }
          } as unknown as UpdateCreatorRequest);

        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        if (analytics?.confirmedCommunicationPreferences)
          analytics.confirmedCommunicationPreferences({
            locale: router?.locale || "",
            contentLanguages: data.contentLanguages?.map((language: Language) => language.label).join(",") || ""
          });
        if (navigateToPage) {
          router.push(navigateToPage);
        } else if (navigateBack) {
          router.push("/connect-accounts");
        } else {
          router.push("/terms-and-conditions");
        }
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
      }
    },
    [stableDispatch, router, onboardingSteps]
  );
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { pending, execute: submitHandleClb } = useAsync<FieldValues, void>(submitHandle as any, false);
  const handleCancelRegistration = useCallback(() => {
    if (analytics.canceledOnboardingFlow)
      analytics.canceledOnboardingFlow({ locale: router?.locale || "", page: router.pathname });
    router.push("/api/logout");
  }, [router]);

  useEffect(() => {
    async function fetchData() {
      try {
        stableDispatch({ type: LOADING, data: true });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const creator: any = legacyCreatorService
          ? (await legacyCreatorService.getCreatorWithProgramCode()).data
          : await creatorService?.getCreator(configuration.programCode);
        if (!legacyCreatorService) {
          creator.communicationPreferences.contentLanguages = creator?.communicationPreferences.contentLanguages.map(
            (language: { code: string; name: string }) => ({
              ...language,
              value: language.code,
              label: language.name
            })
          );
          creator.communicationPreferences.preferredLanguage = {
            ...creator.program.preferredLanguage,
            value: creator?.program?.preferredLanguage?.code,
            label: creator?.program?.preferredLanguage?.name
          };
        }
        setCommunications(creator.communicationPreferences as unknown as CommunicationPreferences);
        const languages = await metadataService.getLanguages();
        if (languages) setLanguages(languages);
        const locales = await metadataService.getLocales();
        if (locales) setLocales(locales);
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
      } finally {
        stableDispatch({ type: LOADING, data: false });
      }
    }
    fetchData();
  }, [stableDispatch]);

  /** ReFetch Creators on window closed */
  useEffect(() => {
    async function refreshCreatorInformation() {
      if ((!popupOpened && showAddConfirmation) || !accountToRemove) {
        try {
          if (isMounted()) {
            stableDispatch({ type: LOADING, data: true });
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const creator: any = legacyCreatorService
              ? (await legacyCreatorService.getCreatorWithProgramCode()).data
              : await creatorService?.getCreator(configuration.programCode);
            if (!legacyCreatorService) {
              creator.communicationPreferences.contentLanguages =
                creator?.communicationPreferences.contentLanguages.map((language: { code: string; name: string }) => ({
                  ...language,
                  value: language.code,
                  label: language.name
                }));
              creator.communicationPreferences.preferredLanguage = {
                ...creator.program.preferredLanguage,
                value: creator?.program?.preferredLanguage?.code,
                label: creator?.program?.preferredLanguage?.name
              };
            }

            setCommunications(creator?.communicationPreferences as unknown as CommunicationPreferences);
            setShowAddConfirmation(false);
          }
        } catch (error) {
          errorHandling(stableDispatch, error as Error);
        } finally {
          stableDispatch({ type: LOADING, data: false });
        }
      }
    }
    refreshCreatorInformation();
  }, [popupOpened, showAddConfirmation, accountToRemove, stableDispatch]);

  useEffect(() => {
    if (user) stableDispatch({ type: SESSION_USER, data: user });
  }, [user, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError ? isError : toastContent(isValidationError as [])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <>
      <div className="communication-preferences-container communication-preferences-page">
        <div className="onboarding-intro">
          <h3 className="onboarding-intro-title">{translation.title}</h3>
          <div className="onboarding-intro-description">{translation.description}</div>
        </div>
        {communications && languages && locales && (
          <Form mode="onChange" onSubmit={submitHandleClb}>
            <CommunicationPreferencesForm
              translation={translation}
              languages={languages}
              locales={locales}
              communications={communications}
              showAddConfirmation={showAddConfirmation}
              setShowAddConfirmation={setShowAddConfirmation}
              labels={labels}
              layout={layout}
              removeAccount={removeAccount}
              setRemoveAccount={setRemoveAccount}
              accountToRemove={accountToRemove}
              setAccountToRemove={setAccountToRemove}
              showRemoveAccountModal={showRemoveAccountModal}
              setShowRemoveAccountModal={setShowRemoveAccountModal}
              onClose={onClose}
              pending={pending}
              showMigration={showMigration}
              setShowMigration={setShowMigration}
              submitHandle={submitHandle}
              stableDispatch={stableDispatch}
              router={router}
              navigateToPage={navigateToPage}
              userNavigated={userNavigated}
              errorHandling={errorHandling}
              state={state}
              configuration={configuration}
              connectAccounts={connectAccounts}
            />
          </Form>
        )}
      </div>
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </>
  );
};

export default CommunicationPreferencesStep;
