import React, { FC, memo, useCallback, useMemo } from "react";
import { AccountCard, AccountType, SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { POPUP_OPENED, useAsync, WINDOW_PARAMS } from "../../utils/index";
import { ChannelType, Dispatch, ErrorHandling, OnboardingStepConfiguration, State } from "../../types";
import DisconnectAccountModal from "./DisconnectAccountModal";
import ConnectedAccountsService from "../../Browser/ConnectedAccountService";
import { Labels } from "../CommunicationPreferencesStep";

export const GET_FB_PAGES = "GET_FB_PAGES";
export const SHOW_FACEBOOK_PAGES = "SHOW_FACEBOOK_PAGES";
export const RELOAD_INTERESTED_CREATOR_ACCOUNTS = "RELOAD_INTERESTED_CREATOR_ACCOUNTS";

export type ConnectAccounts = Array<{ value: string; accountIcon: FC<SvgProps>; redirectUrl: string }>;

export type ConnectedAccount = {
  id: string;
  accountId: string;
  name: string;
  type: ChannelType;
  uri: string;
  thumbnail: string;
  username: string;
  disconnected: boolean;
  isExpired: boolean;
};

export type AccountsMapperProps = {
  id?: string | null;
  accountId?: string | null;
  accountType: AccountType;
  username?: string;
  name?: string;
  labels: Labels;
  setAccountToRemove: (data: string) => void;
  disconnected?: boolean;
  isExpired?: boolean;
  setShowAddConfirmation: (data: boolean) => void;
  showAddConfirmation: boolean;
  setShowRemoveAccountModal: (data: boolean) => void;
  errorHandling: ErrorHandling;
  stableDispatch: Dispatch;
  configuration: OnboardingStepConfiguration;
  connectAccounts: ConnectAccounts;
};
const AccountsMapper = memo(function AccountsMapper({
  id = null,
  accountId = null,
  accountType,
  username = "",
  name = "",
  labels,
  setAccountToRemove,
  disconnected = false,
  isExpired = false,
  setShowAddConfirmation,
  showAddConfirmation,
  setShowRemoveAccountModal,
  errorHandling,
  stableDispatch,
  configuration,
  connectAccounts
}: AccountsMapperProps) {
  const connectedAccountsService = new ConnectedAccountsService(configuration.onBoardingClient);
  const onRemoveAccount = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();
      setAccountToRemove(`${id}-${accountType}`);
      setShowRemoveAccountModal(true);
    },
    [id, accountType, setAccountToRemove, setShowRemoveAccountModal]
  );

  const onReconnectAccount = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (!showAddConfirmation) {
        setShowAddConfirmation(true);
        const redirectUrl = connectAccounts.find(
          (connectAccount) => connectAccount.value.toLowerCase() === accountType.toLowerCase()
        )?.redirectUrl;
        const loginWindow = window.open(redirectUrl, "_blank", WINDOW_PARAMS);
        stableDispatch({ type: POPUP_OPENED, data: true });
        const loop = setInterval(function () {
          if (loginWindow?.closed) {
            clearInterval(loop);
            stableDispatch({ type: POPUP_OPENED, data: false });
            connectedAccountsService
              .clearAccountType()
              .then(() => {
                stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
              })
              .catch((e) => errorHandling(stableDispatch, e));
          }
        }, 100);
      }
    },
    [showAddConfirmation, setShowAddConfirmation, stableDispatch]
  );
  const imgUrl = useMemo(
    () =>
      connectAccounts.find((connectAccount) => connectAccount.value.toLowerCase() === accountType.toLowerCase())
        ?.accountIcon,
    [accountType]
  );

  return (
    <>
      <AccountCard
        accountIcon={imgUrl as FC<SvgProps>}
        expired={!disconnected && isExpired}
        handleRemoveAccount={onRemoveAccount}
        accountId={accountId as string}
        accountType={accountType as AccountType}
        labels={labels}
        username={username || name}
        handleReconnectAccount={onReconnectAccount}
      />
    </>
  );
});

export type AccountsProps = {
  accounts: Array<ConnectedAccount>;
  setAccountToRemove: (data: string) => void;
  accountToRemove: string;
  labels: Labels;
  setShowAddConfirmation: (data: boolean) => void;
  showAddConfirmation: boolean;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (data: boolean) => void;
  state: State;
  errorHandling: ErrorHandling;
  stableDispatch: Dispatch;
  configuration: OnboardingStepConfiguration;
  connectAccounts: ConnectAccounts;
};
const Accounts = memo(function Accounts({
  accounts,
  setAccountToRemove,
  accountToRemove,
  labels,
  setShowAddConfirmation,
  showAddConfirmation,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  errorHandling,
  stableDispatch,
  state,
  configuration,
  connectAccounts
}: AccountsProps) {
  return useMemo(() => {
    return accounts.map((account, ind) => (
      <AccountsMapper
        key={ind}
        {...{
          labels,
          setAccountToRemove,
          accountToRemove,
          ...{ ...account, accountType: account.type as AccountType },
          setShowAddConfirmation,
          showAddConfirmation,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          errorHandling,
          stableDispatch,
          state,
          configuration,
          connectAccounts
        }}
      />
    ));
  }, [accounts, labels, setAccountToRemove, accountToRemove]);
});

export type ConnectedAccountProps = {
  labels: Labels;
  setAccountToRemove: (data: string) => void;
  accountToRemove: string;
  accounts: Array<ConnectedAccount>;
  setShowAddConfirmation: (data: boolean) => void;
  showAddConfirmation: boolean;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (data: boolean) => void;
  state: State;
  errorHandling: ErrorHandling;
  stableDispatch: Dispatch;
  configuration: OnboardingStepConfiguration;
  connectAccounts: ConnectAccounts;
};
export const ConnectedAccountComponent = memo(function ConnectedAccount({
  labels,
  setAccountToRemove,
  accountToRemove,
  accounts,
  setShowAddConfirmation,
  showAddConfirmation,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  state,
  errorHandling,
  stableDispatch,
  configuration,
  connectAccounts
}: ConnectedAccountProps): JSX.Element | null {
  const { removeAccountTitle, removeAccountDescription1, removeAccountDescription2 } = labels.modal;
  const connectedAccountsService = new ConnectedAccountsService(configuration.onBoardingClient);

  const onCloseRemoveModal = useCallback(() => setShowRemoveAccountModal(false), [setShowRemoveAccountModal]);
  const onRemoveAccount = useCallback(async () => {
    const [id] = accountToRemove.match(/\w+(?=\-(\w+)+)/) as string[];
    try {
      await connectedAccountsService.removeDiscordAccount(id);
      setAccountToRemove(null as unknown as string);
      setShowRemoveAccountModal(false);
    } catch (error) {
      setAccountToRemove(null as unknown as string);
      setShowRemoveAccountModal(false);
      errorHandling(stableDispatch, error as Error);
    }
  }, [accountToRemove, stableDispatch, setAccountToRemove]);
  const { pending, execute: onRemove } = useAsync(onRemoveAccount, false);

  const modalLabels = {
    title: removeAccountTitle,
    close: labels.close,
    remove: labels.remove,
    cancel: labels.cancel,
    removeAccountDescription1: removeAccountDescription1,
    removeAccountDescription2: removeAccountDescription2
  };

  return !!accounts?.length ? (
    <div>
      <div className="mg-ic-ui-connected-accounts-container">
        <h5 className="mg-ic-ui-connected-accounts-title">{labels.myAccount}</h5>
        <div className="ic-ui-connected-acc-card-container">
          <Accounts
            {...{
              accounts,
              setAccountToRemove,
              accountToRemove,
              labels,
              setShowAddConfirmation,
              showAddConfirmation,
              showRemoveAccountModal,
              setShowRemoveAccountModal,
              errorHandling,
              stableDispatch,
              state,
              configuration,
              connectAccounts
            }}
          />
        </div>
      </div>
      {showRemoveAccountModal && (
        <DisconnectAccountModal
          labels={modalLabels}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onRemove={onRemove as any}
          onCancel={onCloseRemoveModal}
          isPending={pending}
        />
      )}
    </div>
  ) : null;
});
export default ConnectedAccount;
