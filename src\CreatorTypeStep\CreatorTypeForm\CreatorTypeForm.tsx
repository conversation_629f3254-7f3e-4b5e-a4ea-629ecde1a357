import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import MigrationModal, { LayoutType } from "src/MigrationModal/MigrationModal";
import { USER_NAVIGATED } from "src/utils";
import { Dispatch, State } from "../../types";
import CreatorTypeInputs from "../CreatorTypeInputs/CreatorTypeInputs";
import { CreatorType, FormLabels } from "../creator-type";
import { NextRouter } from "next/router";
import { Url } from "next/dist/shared/lib/router/router";

export type CreatorTypeFormProps = {
  creatorsType: CreatorType[];
  creator: Record<string, unknown> & { creatorTypes: string[] };
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  onClose: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  setShowMigration: (show: boolean) => void;
  showMigration: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submitHandle: any;
  stableDispatch: Dispatch;
  dispatch: Dispatch;
  formLabels: FormLabels & CreatorTypeFormLabels;
  router: NextRouter;
  navigateToPage: Url;
  state: State;
  basePath?: string;
};
export type CreatorsTypeLabels = {
  label: string;
  value: string;
};
export type CreatorTypeFormLabels = {
  cancel: string;
  next: string;
  requiredMessage: string;
  creatorsTypeLabels: Array<CreatorsTypeLabels>;
};

export default memo(function CreatorTypeForm({
  creatorsType,
  creator,
  formLabels,
  breadcrumbLabels,
  onClose,
  isPending,
  setShowMigration,
  showMigration,
  submitHandle,
  stableDispatch,
  dispatch,
  router,
  navigateToPage,
  state,
  basePath
}: CreatorTypeFormProps) {
  const { getValues, formState } = useFormContext();
  const { userNavigated } = state;
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);

  const onSave = () => submitHandle(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    stableDispatch({ type: USER_NAVIGATED, data: false });
    if (navigateToPage) {
      router.push(navigateToPage);
    } else {
      router.push("/onboarding/information");
    }
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      if (navigateToPage) {
        router.push(navigateToPage);
      } else {
        router.push("/onboarding/information");
      }

      dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  const buttons = useMemo(() => {
    const { cancel, save, discard, next } = formLabels;
    return { cancel, save, discard, next };
  }, [formLabels]);

  const layout = {
    buttons
  } as unknown as LayoutType;

  const footerProps = {
    buttons,
    onCancel: onClose,
    disableSubmit: isPending,
    isPending
  };

  return (
    <>
      <CreatorTypeInputs {...{ formLabels, creatorsType, values: creator.creatorTypes || [], footerProps, basePath }} />
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard, layout, breadcrumbLabels }} />
    </>
  );
});
