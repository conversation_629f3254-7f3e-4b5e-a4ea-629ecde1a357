import { Factory } from "fishery";
import Random from "./Random";

interface getStatusResponse {
  upToDate: boolean;
}

interface getUrlResponse {
  contractUrl: string;
}

const factory = Factory.define(() => ({
  upToDate: Random.boolean(),
  url: Random.url()
}));

/**
 * Returns an outdated status
 */
export function aTermsAndConditionsStatus(override = { upToDate: false }): getStatusResponse {
  return factory.build(override) as getStatusResponse;
}

export function getPactSafeUrl(): getUrlResponse {
  return factory.build({
    contractUrl:
      'https://app.pactsafe.com/sign?r=60d22c838ea821120cee5998&s=60c2f746704ffb0e40d92edc&signature=leSvpmgIQMLPi7Ua2Pl4Z0i5AkTRFzO6km3Q2FLMzeSkx9ZIRA7Bb59PCiFsv3vQ4oQTd-4~0kNsOMykCtwi5Vp9Qe9aqmaC~UNTPuwiCnhYSIdkfG88YkWUF1xdFDOZUWkcdPG-~sVLn8MAj8p0vtlYczeydgMsdVHWcRkjBYa3Z~BoqMs1bkt0m7tFovXsh2Aenos3CKaDB118ipQ1CbGmwcdbbWcVptIaqo0ES85cKV-5Cx~vEqnnO18uy6IdAeOsFsbKEQBh2kYFZymGdCgfv65fH6vof9hRaE--TCUnYR~QZm01uO1Cpb02fI320wg1eBWjNWivJucA7-lyrg__","token":"aL0BIJp~PBpgsMm4mte4aG-mdtwVjkxOV4STqA0lxy4_'
  }) as getUrlResponse;
}
