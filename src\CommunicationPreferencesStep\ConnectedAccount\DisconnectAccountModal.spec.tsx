import React from "react";
import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import DisconnectAccountModal from "./DisconnectAccountModal";
import { renderPage } from "../../Helpers/Page";
import { ConnectAccountLabels } from "../../Translations/ConnectAccounts";

describe("DisconnectAccountModal", () => {
  const disconnectAccountModalProps = {
    labels: {
      title: ConnectAccountLabels.messages.removeAccountTitle,
      remove: ConnectAccountLabels.remove,
      cancel: ConnectAccountLabels.cancel,
      close: ConnectAccountLabels.close,
      removeAccountDescription1: ConnectAccountLabels.messages.removeAccountDescription,
      removeAccountDescription2: ConnectAccountLabels.description
    },
    onRemove: jest.fn(),
    onCancel: jest.fn(),
    isPending: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows disconnect account modal", () => {
    renderPage(<DisconnectAccountModal {...disconnectAccountModalProps} />);

    expect(screen.getByRole("heading", { name: disconnectAccountModalProps.labels.title })).toBeInTheDocument();
    expect(screen.getByText(disconnectAccountModalProps.labels.removeAccountDescription1)).toBeInTheDocument();
    expect(screen.getByText(disconnectAccountModalProps.labels.removeAccountDescription2)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: disconnectAccountModalProps.labels.cancel })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: disconnectAccountModalProps.labels.remove })).toBeInTheDocument();
  });

  it("executes cancel handler when 'Cancel' button is clicked", async () => {
    renderPage(<DisconnectAccountModal {...disconnectAccountModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: disconnectAccountModalProps.labels.cancel }));

    expect(disconnectAccountModalProps.onCancel).toHaveBeenCalledTimes(1);
  });

  it("disable 'Remove' button", () => {
    renderPage(<DisconnectAccountModal {...disconnectAccountModalProps} isPending />);

    expect(screen.getByRole("button", { name: disconnectAccountModalProps.labels.remove })).toBeDisabled();
  });

  it("executes remove handler when 'Remove' button is clicked", async () => {
    renderPage(<DisconnectAccountModal {...disconnectAccountModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: disconnectAccountModalProps.labels.remove }));

    expect(disconnectAccountModalProps.onRemove).toHaveBeenCalledTimes(1);
  });
});
