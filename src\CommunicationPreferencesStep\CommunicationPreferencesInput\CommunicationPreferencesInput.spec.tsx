import React, { ReactNode } from "react";
import { aCommunicationPreferences } from "../../Factories/creators/CommunicationPreferences";
import { aLanguage } from "../../Factories/LanguageFactories";
import CommunicationPreferencesInput, { CommunicationPreferencesInputProps } from "./CommunicationPreferencesInput";
import { render, screen } from "@testing-library/react";
import CommunicationPreferences from "../../shared/creators/CommunicationPreferences";
import { FormProvider, useForm } from "react-hook-form";

describe("CommunicationPreferencesInput", () => {
  const Wrapper = ({ children }: { children: ReactNode }) => {
    const formMethods = useForm();
    return <FormProvider {...formMethods}>{children}</FormProvider>;
  };

  const labels = {
    title: "Connect Account",
    myAccount: "My Account",
    removeAccount: "Remove Account",
    expireAccount: "Expire Account",
    or: "or",
    reconnectAccount: "Reconnect Account",
    close: "Close",
    addAccount: "Add Account",
    connectAnAccount: "Connect an Account",
    modal: {
      removeAccountTitle: "Remove Account Title",
      removeAccountDescription1: "Remove Account Description 1",
      removeAccountDescription2: "Remove Account Description 2"
    },
    remove: "Remove",
    cancel: "Cancel",
    removeAccountDescription1: "Remove Account Description 1",
    removeAccountDescription2: "Remove Account Description 2",
    communicationSettings: "Communication Settings",
    discord: "Discord",
    email: "Email",
    verificationPending: "Verification Pending",
    reVerifyAccount: "Re-verify Account",
    connect: "Connect",
    save: "Save",
    discard: "Discard",
    layout: {
      main: {
        unhandledError: "Unhandled Error"
      },
      buttons: {
        yes: "Yes",
        no: "No",
        close: "Close",
        cancel: "Cancel Onboarding",
        discard: "Discard",
        save: "Save",
        next: "Next"
      }
    },
    translation: {
      messages: {
        preferredEmailTooLong: "Preferred email is too long",
        preferredEmailInvalid: "Preferred email is invalid",
        preferredPhoneNumber: "Preferred phone number is invalid",
        preferredPhoneNumberTooLong: "Preferred phone number is too long",
        contentLanguage: "Content language is required",
        language: "Language is required",
        preferredEmail: "Preferred email is required"
      },
      labels: {
        preferredEmail: "Preferred Email",
        preferredEmailAddressTitle: "Preferred Email Address",
        preferredEmailAddressDescription: "Please enter your preferred email address.",
        preferredPhoneNumberTitle: "Add Your Preferred Phone Number",
        preferredPhoneNumberDescription: "Please enter your preferred phone number.",
        preferredPhoneNumber: "Preferred Phone Number",
        contentLanguageTitle: "Content Language",
        contentLanguage: "Content Language",
        languageTitle: "Please select your Language",
        contentLanguagesDescription: "Please select your preferred content languages.",
        language: "Language",
        languageDescription: "Please select your preferred language.",
        contentLanguagesTitle: "Content Languages",
        discordTitle: "Discord",
        discordDescription: "Please enter your Discord username.",
        addDiscord: "Add Discord"
      },
      confirmationDesc1: "Confirmation Description 1",
      confirmationDesc2: "Confirmation Description 2",
      modalConfirmationTitle: "Modal Confirmation Title",
      title: "Communication Preferences",
      description: "Please select your preferred communication methods."
    },
    breadcrumbLabels: {
      modalTitle: "Modal Title",
      modalMessage: "Modal Message"
    }
  };
  const languages = [aLanguage({ value: "en", label: "English" })];
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const communications = aCommunicationPreferences({
    preferredLanguage: languages,
    contentLanguages: locales[0]
  }) as unknown as CommunicationPreferences;
  const communicationPreferencesInputProps = {
    languages: languages,
    locales: locales,
    communications: communications,
    labels: labels,
    accounts: [],
    removeAccount: false,
    setRemoveAccount: jest.fn(),
    setAccountToRemove: jest.fn(),
    accountToRemove: "",
    setShowAddConfirmation: jest.fn(),
    showAddConfirmation: false,
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    state: {},
    errorHandling: jest.fn(),
    stableDispatch: jest.fn(),
    configuration: { metadataClient: {}, onBoardingClient: {}, DEFAULT_AVATAR_IMAGE: "" },
    connectAccounts: [
      {
        value: "discord",
        imageUrl: `/img/icons/discord.svg`,
        redirectUrl: `/api/discord-login`
      }
    ]
  } as unknown as CommunicationPreferencesInputProps;

  it("shows communication preferences input", async () => {
    render(
      <Wrapper>
        <CommunicationPreferencesInput {...communicationPreferencesInputProps} />
      </Wrapper>
    );

    expect(screen.getByPlaceholderText(labels.translation.labels.preferredEmail)).toHaveValue(communications.email);
    expect(screen.getByPlaceholderText(labels.translation.labels.preferredPhoneNumber)).toHaveValue(
      communications.phone
    );
  });
});
