@import "../CreatorTypeStep/creator-type.css";
@import "../CreatorTypeStep/CreatorTypePage/CreatorTypePage.css";
@import "../TermsAndConditionsStep/terms-and-conditions.css";
@import "../FranchisesYouPlayStep/franchises-you-play.css";
@import "../CreatorCode/CreatorCodeForm.css";
@import "../MarketplaceDisplayNameStep/MarketplaceDisplayNameStep.css";
@import "../common/CancelRegistrationModal/CancelRegistrationModal.css";
@import "../common/cards/Card/Card.css";
@import "../common/Footer/Footer.css";
@import "../InformationStep/OnboardingInformationStep.css";
@import "../InformationStep/upload/UpdateProfilePicture.css";
@import "../InformationStep/additionalContentAndWebsiteLinks/AdditionalContentAndWebsiteLinks.css";
@import "../CommunicationPreferencesStep/CommunicationPreferencesStep.css";

input:disabled:-webkit-autofill,
input:disabled:-webkit-autofill:hover,
input:disabled:-webkit-autofill:focus,
input:disabled:-webkit-autofill:active {
  transition: background-color 5000s;
  -webkit-text-fill-color: #fff !important;
  -webkit-background-clip: text;
  color: white !important;
}

.mg-page-creator-types-container {
  @apply flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}
