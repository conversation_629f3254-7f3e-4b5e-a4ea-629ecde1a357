import React, { FC, ReactElement } from "react";
import { BrowserAnalytics, CloseH<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, OnboardingStepConfiguration, State } from "../types";
import { NextRouter } from "next/router";
import FranchiseYouPlayPage from "./FranchiseYouPlayPage/FranchiseYouPlayPage";

export type LayoutButtons = {
  yes: string;
  no: string;
  cancel: string;
  next: string;
  submit: string;
  close: string;
  discard: string;
  save: string;
};

export type FranchiseMessages = {
  primaryFranchise: string;
};

export type FranchiseLabels = {
  primaryFranchise: string;
  loadMore: string;
};

export type FranchisesYouPlayLabels = {
  title: string;
  description: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  buttons: LayoutButtons;
};

export type FranchisesYouPlayFormLabels = {
  buttons?: LayoutButtons;
  messages: FranchiseMessages;
  labels: FranchiseLabels;
  primaryFranchiseTitle: string;
  primaryFranchiseSubTitle: string;
  secondaryFranchiseTitle: string;
  secondaryFranchiseSubTitle: string;
};

export type FranchisesYouPlayFallbackImages = {
  primaryFranchisefallbackImage: string;
  secondaryFranchisefallbackImage: string;
};

export type FranchiseItem = {
  value: string;
  label: string;
  image: string;
};

export type FranchisesYouPlayStepConfiguration = OnboardingStepConfiguration & {
  supportedFranchises?: string[];
};

export type FranchisesYouPlayProps = {
  analytics: BrowserAnalytics;
  labels: Labels;
  state: State;
  stableDispatch: Dispatch;
  errorHandling: ErrorHandling;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  configuration: FranchisesYouPlayStepConfiguration;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  showMigration: boolean;
  setShowMigration: (value: boolean) => void;
  navigateToPage: string;
  layout: LayoutButtons;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  dispatch: Dispatch;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  PROGRAM_CODE: string;
  router: NextRouter;
  locale: string;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  onContinueToNextPage?: () => void;
  basePath?: string;
};

export type Labels = {
  franchisesYouPlay: FranchisesYouPlayLabels;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  layout: {
    buttons: LayoutButtons;
    main: {
      unhandledError: string;
    };
  };
};

const FranchisesYouPlay: FC<FranchisesYouPlayProps> = ({
  analytics,
  labels,
  state,
  stableDispatch,
  configuration,
  errorHandling,
  showMigration,
  setShowMigration,
  onClose,
  showConfirmation,
  setShowConfirmation,
  breadcrumbLabels,
  router,
  locale,
  navigateToPage,
  dispatch,
  PROGRAM_CODE,
  franchisesYouPlayFallbackImages,
  errorToast,
  basePath,
  onContinueToNextPage
}) => {
  const { franchisesYouPlay, layout, franchisesYouPlayFormLabels } = labels;
  const layoutButton = {
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    cancel: layout.buttons.cancel,
    next: layout.buttons.next,
    submit: layout.buttons.submit,
    close: layout.buttons.close,
    discard: layout.buttons.discard,
    save: layout.buttons.save
  };

  return (
    <div className="onboarding-creator">
      <FranchiseYouPlayPage
        franchisesYouPlayLabels={{ ...franchisesYouPlay, buttons: layoutButton }}
        franchisesYouPlayFormLabels={{ ...franchisesYouPlayFormLabels, buttons: layoutButton }}
        stableDispatch={stableDispatch}
        showConfirmation={showConfirmation}
        setShowConfirmation={setShowConfirmation}
        onClose={onClose}
        navigateToPage={navigateToPage}
        breadcrumbLabels={breadcrumbLabels}
        dispatch={dispatch}
        unhandledError={labels.layout.main.unhandledError}
        router={router}
        locale={locale}
        analytics={analytics}
        errorHandling={errorHandling}
        PROGRAM_CODE={PROGRAM_CODE}
        showMigration={showMigration}
        setShowMigration={setShowMigration}
        errorToast={errorToast}
        configuration={configuration}
        franchisesYouPlayFallbackImages={franchisesYouPlayFallbackImages}
        state={state}
        basePath={basePath}
        onContinueToNextPage={onContinueToNextPage}
      />
    </div>
  );
};

export default FranchisesYouPlay;
