import React from "react";
import { act, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  discordIcon,
  franchisesYouPlay,
  information,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { aLanguage, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { axe } from "jest-axe";
import { aUser } from "../Factories/User/User";
import { renderPage } from "../Helpers/Page";
import CommunicationPreferencesStep from "./CommunicationPreferencesStep";
import CreatorService from "../Browser/CreatorService";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { aCountry } from "../Factories/CountriesFactories";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { useRouter } from "next/router";
import { aCommunicationPreferences } from "../Factories/creators/CommunicationPreferences";
import { CommunicationPreferencesLabels } from "../Translations/CommunicationPreferences";
import { aCreatorWithProgramCode } from "../../src/Factories/creators/CreatorWithProgramCode";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../Browser/CreatorService");
jest.mock("next/router", () => ({
  useRouter: jest.fn(() => ({ locale: "en-us", push: jest.fn(), pathname: "/communication-preferences" }))
}));

describe("CommunicationPreferencesStep", () => {
  const analytics = {};
  const router = useRouter();
  const platforms = aPlatform({ value: "xbox", label: "xbox" });
  const countries = [aCountry({ label: "Canada", value: "Canada" }), aCountry()];
  const languages = [aLanguage({ value: "en", label: "English" })];
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/information",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/franchises-you-play",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/creator-type",
      isCompleted: false
    },
    {
      icon: connectAccounts,
      title: "Connect Accounts",
      href: "/connect-accounts",
      isCompleted: false
    },
    {
      icon: communicationPreferences,
      title: "Communication Preferences",
      href: "/communication-preferences",
      isCompleted: false
    },
    {
      icon: termsAndConditions,
      title: "Terms And Conditions",
      href: "/terms-and-conditions",
      isCompleted: false
    }
  ];
  const Configuration = {
    metadataClient: {} as TraceableHttpClient,
    creatorsClient: undefined,
    onBoardingClient: {} as TraceableHttpClient,
    programCode: "the_sims",
    DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png"
  };
  const creatorService = {
    getCreatorWithProgramCode: jest.fn().mockResolvedValue({
      data: aCreatorWithProgramCode()
    }),
    update: jest.fn()
  } as unknown as CreatorService;
  const metadataService = {
    getCountriesMatching: jest.fn().mockResolvedValue(countries),
    getLanguages: jest.fn().mockResolvedValue(languages),
    getLocales: jest.fn().mockResolvedValue(locales),
    getPlatformsMatching: jest.fn().mockResolvedValue([platforms])
  } as unknown as MetadataService;
  const communicationPreferencesProps = {
    user: aUser(),
    analytics,
    configuration: Configuration,
    errorHandling: () => {},
    router: router,
    errorToast: () => {},
    stableDispatch: jest.fn(),
    setShowConfirmation: () => {},
    showConfirmation: false,
    setShowMigration: () => {},
    showMigration: false,
    state: {
      onboardingSteps: [
        {
          step: communicationPreferences,
          completed: false,
          label: "Communication Preferences",
          href: "/communication-preferences",
          disabled: false
        },
        {
          step: termsAndConditions,
          completed: false,
          label: "Terms and Conditions",
          href: "/terms-and-conditions",
          disabled: false
        }
      ]
    },
    labels: CommunicationPreferencesLabels,
    navigateToPage: "/terms-and-conditions",
    connectAccounts: [
      {
        value: "discord",
        accountIcon: discordIcon,
        redirectUrl: `/api/discord-login`
      }
    ]
  };
  const { labels } = communicationPreferencesProps;

  beforeEach(() => {
    jest.clearAllMocks();
    (MetadataService as jest.Mock).mockReturnValue(metadataService);
    (CreatorService as jest.Mock).mockReturnValue(creatorService);
  });

  it("saves communication preferences", async () => {
    renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} />);

    expect(await screen.findByText(labels.translation.title)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.description)).toBeInTheDocument();
    expect(await screen.findAllByText(labels.translation.labels.discordTitle)).toHaveLength(2);
    expect(await screen.findByText(labels.translation.labels.preferredEmailAddressTitle)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.preferredPhoneNumberTitle)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.contentLanguagesTitle)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.languageTitle)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.discordDescription)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.preferredEmailAddressDescription)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.contentLanguagesDescription)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.languageDescription)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.preferredEmail)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.preferredPhoneNumber)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.contentLanguage)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.labels.language)).toBeInTheDocument();
  });

  it("shows modal with logout option", async () => {
    renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} showConfirmation />);

    await userEvent.click(await screen.findByRole("button", { name: labels.layout.buttons.cancel }));

    expect(await screen.findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.modalConfirmationTitle)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.confirmationDesc1)).toBeInTheDocument();
    expect(await screen.findByText(labels.translation.confirmationDesc2)).toBeInTheDocument();
  });

  it("logs 'Canceled Onboarding Flow' event when clicking on 'Yes' button in the cancel registration modal", async () => {
    const analytics = { canceledOnboardingFlow: jest.fn() };
    renderPage(
      <CommunicationPreferencesStep {...communicationPreferencesProps} analytics={analytics} showConfirmation />
    );
    await userEvent.click(await screen.findByRole("button", { name: labels.layout.buttons.cancel }));

    await userEvent.click(await screen.findByRole("button", { name: /Yes/i }));

    await waitFor(() => {
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledTimes(1);
      expect(analytics.canceledOnboardingFlow).toHaveBeenCalledWith({
        locale: "en-us",
        page: "/communication-preferences"
      });
      expect(router.push).toHaveBeenCalledWith("/api/logout");
    });
  });

  it("logs 'Confirmed Communication Preferences' event when clicking on 'Next' button in the cancel registration modal", async () => {
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockResolvedValue({
      data: aCreatorWithProgramCode({
        communicationPreferences: aCommunicationPreferences({
          contentLanguages: [
            aLanguage({ label: "English", code: "en", name: "English" }),
            aLanguage({ label: "Japanese", code: "jp", name: "Japanese" })
          ]
        })
      })
    });
    (creatorService.update as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { confirmedCommunicationPreferences: jest.fn() };
    renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} analytics={analytics} />);

    await userEvent.click(await screen.findByRole("button", { name: /Next/i }));

    await waitFor(() => {
      expect(creatorService.update).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledTimes(1);
      expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledWith({
        locale: "en-us",
        contentLanguages: "English,Japanese"
      });
    });
  });

  it("navigates to 'connect-accounts' page when user click on 'Back' button", async () => {
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockResolvedValue({
      data: aCreatorWithProgramCode()
    });
    (creatorService.update as jest.Mock).mockImplementation(() => Promise.resolve());
    renderPage(
      <CommunicationPreferencesStep
        {...communicationPreferencesProps}
        navigateToPage={""}
        state={{
          userNavigated: true,
          onboardingSteps: steps
        }}
      />
    );

    await userEvent.click(await screen.findByRole("button", { name: labels.layout.buttons.next }));

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/connect-accounts"));
  });

  it("shows confirmation modal when user click on 'Back' button with form has unsaved changes", async () => {
    const analytics = { confirmedCommunicationPreferences: jest.fn() };
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockResolvedValue({
      data: aCreatorWithProgramCode()
    });
    (creatorService.update as jest.Mock).mockImplementation(() => Promise.resolve());
    renderPage(
      <CommunicationPreferencesStep {...communicationPreferencesProps} analytics={analytics} showConfirmation />
    );
    const phoneNumber = await screen.findByLabelText(labels.translation.labels.preferredPhoneNumber);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");

    await userEvent.click(await screen.findByRole("button", { name: labels.layout.buttons.cancel }));

    // There are two cancel buttons in this page used within to find the one in the modal
    const { findByRole, findByText } = within(await screen.findByRole("dialog"));
    expect(await findByRole("heading", { name: labels.translation.modalConfirmationTitle })).toBeInTheDocument();
    expect(await findByText(labels.translation.confirmationDesc1)).toBeInTheDocument();
    expect(await findByRole("button", { name: /Close$/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /no/i })).toBeInTheDocument();
    expect(await findByRole("button", { name: /yes/i })).toBeInTheDocument();
    expect(router.push).not.toBeCalled();
    expect(analytics.confirmedCommunicationPreferences).not.toBeCalled();
  });

  it("closes the confirmation modal on discard", async () => {
    const setShowConfirmationMock = jest.fn();
    const { rerender } = renderPage(
      <CommunicationPreferencesStep
        {...communicationPreferencesProps}
        setShowConfirmation={setShowConfirmationMock}
        showConfirmation={true}
      />
    );
    const phoneNumber = await screen.findByLabelText(labels.translation.labels.preferredPhoneNumber);
    await userEvent.clear(phoneNumber);
    await userEvent.type(phoneNumber, "*********");
    await userEvent.click(await screen.findByRole("button", { name: labels.layout.buttons.cancel }));
    const { findByRole, queryByRole, queryByText } = within(await screen.findByRole("dialog"));

    await userEvent.click(await findByRole("button", { name: /no/i }));

    expect(setShowConfirmationMock).toHaveBeenCalledWith(false);
    rerender(
      <CommunicationPreferencesStep
        {...communicationPreferencesProps}
        setShowConfirmation={setShowConfirmationMock}
        showConfirmation={false}
      />
    );

    expect(queryByRole("heading", { name: labels.translation.modalConfirmationTitle })).not.toBeInTheDocument();
    expect(queryByText(labels.translation.confirmationDesc1)).not.toBeInTheDocument();
  });

  it("enables the button when communications data is having default values for form", async () => {
    (creatorService.getCreatorWithProgramCode as jest.Mock).mockResolvedValue({
      data: aCreatorWithProgramCode()
    });

    renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} />);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /next/i })).toBeEnabled();
    });
  });

  it("shows phone number error message only after the field has been touched", async () => {
    renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} />);
    const phoneNumberInput = await screen.findByLabelText(labels.translation.labels.preferredPhoneNumber);
    expect(screen.queryByText(labels.translation.messages.preferredPhoneNumber)).not.toBeInTheDocument();
    await userEvent.click(phoneNumberInput);
    await userEvent.clear(phoneNumberInput);

    await userEvent.tab();

    await waitFor(() => {
      expect(screen.queryByText(labels.translation.messages.preferredPhoneNumber)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(<CommunicationPreferencesStep {...communicationPreferencesProps} />);

    await act(async () => {
      results = await axe(container);
    });
    expect(results).toHaveNoViolations();
  });
});
