import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorType, { Labels } from "./creator-type";

const meta: Meta<typeof CreatorType> = {
  title: "Component Library/Creator Type Page",
  component: CreatorType,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CreatorType>;

const dispatch = (): void => {};

export const CreatorTypePage: Story = {
  args: {
    analytics: { completedOnboardingFlow: () => {} },
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: {
      cancel: "Cancel",
      next: "Next",
      yes: "Yes",
      no: "No",
      close: "Close",
      creatorTitle: "Choose your creator types",
      creatorDescription: "Select the types of creator that fits the content you create.",
      confirmationDesc1: "Confirmation Description 1",
      confirmationDesc2: "Confirmation Description 2",
      modalConfirmationTitle: "Modal Confirmation Title",
      unhandledError: "Unhandled Error",
      requiredMessage: "This field is required",
      creatorsTypeLabels: [
        {
          value: "YOUTUBER",
          label: "YouYuber"
        },
        {
          value: "LIFESTYLE",
          label: "Lifestyle"
        },
        {
          value: "PHOTOGRAPHER",
          label: "Photographer"
        },
        {
          value: "DESIGNER_ARTIST",
          label: "Designer/Artist"
        },
        {
          value: "BLOGGER",
          label: "Blogger"
        },
        {
          value: "LIVE_STREAMER",
          label: "Live Streamer"
        },
        {
          value: "PODCASTER",
          label: "Podcaster"
        },
        {
          value: "COSPLAYER",
          label: "Cosplayer"
        },
        {
          value: "ANIMATOR",
          label: "Animator"
        },
        {
          value: "SCREENSHOTER",
          label: "Screenshoter"
        },
        {
          value: "OTHER",
          label: "Other"
        }
      ]
    } as Labels,
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: dispatch,
    setShowConfirmation: (_a: boolean) => {},
    showConfirmation: false,
    locale: "en-us",
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
      metadataClient: {
        get: () =>
          Promise.resolve({
            data: [
              {
                value: "YOUTUBER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/YOUTUBER.png",
                label: "youtuber"
              },
              {
                value: "LIFESTYLE",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIFESTYLE.png",
                label: "lifestyle"
              },
              {
                value: "PHOTOGRAPHER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PHOTOGRAPHER.png",
                label: "photographer"
              },
              {
                value: "DESIGNER_ARTIST",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/DESIGNER_ARTIST.png",
                label: "designer_artist"
              },
              {
                value: "BLOGGER",
                imageAsIcon: "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/BLOGGER.png",
                label: "blogger"
              },
              {
                value: "LIVE_STREAMER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIVE_STREAMER.png",
                label: "live_streamer"
              },
              {
                value: "PODCASTER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PODCASTER.png",
                label: "podcaster"
              },
              {
                value: "COSPLAYER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/COSPLAYER.png",
                label: "cosplayer"
              },
              {
                value: "ANIMATOR",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/ANIMATOR.png",
                label: "animator"
              },
              {
                value: "SCREENSHOTER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/SCREENSHOTER.png",
                label: "screenshoter"
              },
              {
                value: "OTHER",
                imageAsIcon: "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/OTHER.png",
                label: "other"
              }
            ]
          }),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      onBoardingClient: {} as unknown as TraceableHttpClient
    }
  }
};
