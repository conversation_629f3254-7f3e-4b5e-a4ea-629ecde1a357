import React, { ComponentType, ReactElement } from "react";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import CreatorCodeForm, { CreatorPageLabels } from "./CreatorCodeForm";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { PageLabels } from "src/InformationStep/OnboardingInformationStep";

const meta: Meta<typeof CreatorCodeForm> = {
  title: "Component Library/Creator Code Form Component",
  component: CreatorCodeForm,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CreatorCodeForm>;

const dispatch = (): void => {};

export const CreatorCodeFormComponent: Story = {
  args: {
    pageLabels: {
      creatorTitle: "Creator Title",
      yes: "Yes",
      no: "No",
      userDescription1: "User Description 1",
      userDescription2: "User Description 2",
      modalConfirmationTitle: "Are you sure you want to cancel registration?",
      confirmationDesc1: "Pressing Yes will quit the registration process.",
      confirmationDesc2: "You can begin registration again at any point by visiting the Creator Network Website.",
      title: "Setup Your Unique Creator Code",
      description:
        "Create a unique code that represents your brand or identity. This code will help fans and followers recognize you easily. Ensure your code reflects your content and is simple for your audience to remember.",
      tipsTitle: "Tips for creating a great code:",
      tip1: "Choose something that aligns with your persona or channel name.",
      tip2: "Keep it concise and memorable.",
      codeRequirements:
        "Creator codes can only contain letters and numbers, up to a maximum of 22 characters, with no special symbols",
      customizeYourCode: "Customize your code",
      confirmYourCode: "Confirm your code",
      next: "Next",
      cancel: "Cancel"
    } as CreatorPageLabels & PageLabels,
    infoLabels: {
      customizeCodeRequired: "Creator code is required",
      confirmCodeRequired: "Confirm code is required",
      tooLong: "Creator codes can be up to 22 characters long",
      specialCharactersNotAllowed: "Only characters and numbers are allowed. No special characters or spaces.",
      doNotMatch: "Codes do not match. Please try again.",
      alreadyInUse: "This code is already in use. Please try a different one.",
      codeIsInvalid: "The creator code is invalid. It should only contain alphanumeric characters.",
      inappropriateLanguage: "This code contains inappropriate language. Please choose a different code."
    },
    onClose: () => {},
    errorHandling: () => {},
    layout: {
      buttons: {
        yes: "Yes",
        close: "Close",
        no: "No"
      }
    },
    stableDispatch: dispatch,
    infoUnionPath: "/img/union.svg",
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      onBoardingClient: {} as unknown as TraceableHttpClient,
      metadataClient: {} as unknown as TraceableHttpClient,
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png"
    }
  }
};
