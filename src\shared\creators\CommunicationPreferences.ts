import PreferredLanguage, { PreferredLanguageResponse } from "../languages/PreferredLanguage";

export type CommunicationPreferencesResponse = {
  email: string | null;
  phone: string | null;
  discord: string | null;
  preferredLanguage: PreferredLanguageResponse | null;
  contentLanguages: Array<PreferredLanguageResponse>;
};

export default class CommunicationPreferences {
  static fromApi(data: CommunicationPreferencesResponse): CommunicationPreferences {
    return new CommunicationPreferences(
      data.email,
      data.phone,
      data.discord,
      data.preferredLanguage ? PreferredLanguage.fromApi(data.preferredLanguage) : null,
      data.contentLanguages.map((item) => PreferredLanguage.fromApi(item))
    );
  }

  constructor(
    readonly email: string | null,
    readonly phone: string | null,
    readonly discord: string | null,
    readonly preferredLanguage: PreferredLanguage | null,
    readonly contentLanguages: Array<PreferredLanguage>
  ) {}
}
