import MailingAddress from "./MailingAddress";
import CommunicationPreferences from "./CommunicationPreferences";
import AdditionalInformation from "./AdditionalInformation";
import LegalEntityInformation from "./LegalEntityInformation";
import ConnectedAccount from "../channels/ConnectedAccount";
import { Overwrite } from "../../types";
import AccountInformation, { AccountInformationResponse } from "./AccountInformation";
import { CreatorWithPayableStatusResponse } from "./CreatorWithPayableStatus";
import PreferredPlatform from "../platforms/PreferredPlatform";
import PreferredFranchise from "../franchises/PreferredFranchise";
import CreatorWithExpiredAccounts from "./CreatorWithExpiredAccounts";

export type CreatorWithProgramCodeResponse = Overwrite<
  CreatorWithPayableStatusResponse,
  { accountInformation: AccountInformationResponse }
> & {
  creatorConnectedProgram: string;
  socialLinks: Array<string>;
};

export default class CreatorWithProgramCode extends CreatorWithExpiredAccounts {
  static fromApi(data: CreatorWithProgramCodeResponse): CreatorWithProgramCode {
    return new CreatorWithProgramCode(
      data.id,
      data.avatar,
      data.creatorTypes || [],
      AccountInformation.fromApi(data.accountInformation),
      data.preferredPlatforms.map((item) => PreferredPlatform.fromApi(item)),
      data.preferredFranchises.map((item) => PreferredFranchise.fromApi(item)),
      MailingAddress.fromApi(data.mailingAddress),
      CommunicationPreferences.fromApi(data.communicationPreferences),
      data.connectedAccounts || [],
      AdditionalInformation.fromApi(data.additionalInformation),
      data.creatorConnectedProgram,
      data.socialLinks || [],
      data.legalInformation ? LegalEntityInformation.fromApi(data?.legalInformation) : undefined
    );
  }

  constructor(
    readonly id: string,
    readonly avatar: string | null,
    readonly creatorTypes: Array<string>,
    public accountInformation: AccountInformation,
    public preferredPlatforms: Array<PreferredPlatform>,
    public preferredFranchises: Array<PreferredFranchise>,
    readonly mailingAddress: MailingAddress,
    readonly communicationPreferences: CommunicationPreferences,
    readonly connectedAccounts: Array<ConnectedAccount>,
    readonly additionalInformation: AdditionalInformation,
    readonly creatorConnectedProgram: string,
    readonly socialLinks: Array<string>,
    readonly legalEntity?: LegalEntityInformation
  ) {
    super(
      id,
      avatar,
      creatorTypes,
      accountInformation,
      preferredPlatforms,
      preferredFranchises,
      mailingAddress,
      communicationPreferences,
      connectedAccounts,
      additionalInformation,
      legalEntity
    );
  }
}
