import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AxiosResponse } from "axios";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import CreatorWithProgramCode, { CreatorWithProgramCodeResponse } from "../shared/creators/CreatorWithProgramCode";
import PreferredFranchise from "../shared/franchises/PreferredFranchise";
import PreferredPlatform from "../shared/platforms/PreferredPlatform";
import { CommunicationPreferencesResponse } from "../shared/creators/CommunicationPreferences";

export class CreatorProfile {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly accountInformation: any;
  readonly creatorTypes: string[] = [];
  preferredPrimaryFranchises: { label: string } = { label: "" };
  preferredPrimaryPlatforms: { label: string } = { label: "" };
  preferredSecondaryFranchises: Array<PreferredFranchise> = [];
  preferredSecondaryPlatforms: Array<PreferredPlatform> = [];
  connectedChannels: Array<Record<string, string>> = [];

  constructor(creator: {
    accountInformation: { registrationDate: number | LocalizedDate; dateOfBirth: number | LocalizedDate };
  }) {
    Object.assign(this, creator);
    creator.accountInformation.registrationDate = new LocalizedDate(
      creator.accountInformation.registrationDate as number
    );
    creator.accountInformation.dateOfBirth = new LocalizedDate(creator.accountInformation.dateOfBirth as number);
  }

  formattedRegistrationDate(locale: string): string {
    return (this.accountInformation.registrationDate as unknown as LocalizedDate).formatLong(locale);
  }

  dateOfBirth(): Date {
    return (this.accountInformation.dateOfBirth as unknown as LocalizedDate).toDate();
  }

  preferredPrimaryFranchiseLabel(): string {
    return this.preferredPrimaryFranchises.label;
  }

  preferredSecondaryFranchisesLabels(): string[] {
    return this.preferredSecondaryFranchises.map((franchise) => franchise.label);
  }

  preferredPrimaryPlatformLabel(): string {
    return this.preferredPrimaryPlatforms.label;
  }

  preferredSecondaryPlatformsLabels(): string[] {
    return this.preferredSecondaryPlatforms.map((platform) => platform.label);
  }

  socialAccountTypes(): string[] {
    return this.connectedChannels.map((account) => account.type);
  }

  addedCreatorTypes(selectedTypes: string[]): string[] | undefined {
    if (this.creatorTypes) {
      return this.difference(selectedTypes, this.creatorTypes);
    }
  }

  removedCreatorTypes(selectedTypes: string[]): string[] | undefined {
    if (this.creatorTypes) {
      return this.difference(this.creatorTypes, selectedTypes);
    }
  }

  updatedSecondaryFranchises(selectedFranchises: string[]): boolean {
    return (
      this.difference(
        selectedFranchises,
        this.preferredSecondaryFranchises.map((platform) => platform.label)
      ).length > 0 ||
      this.difference(
        this.preferredSecondaryFranchises.map((platform) => platform.label),
        selectedFranchises
      ).length > 0
    );
  }

  updatedPrimaryFranchise(selectedFranchise: string): boolean {
    return this.preferredPrimaryFranchises.label !== selectedFranchise;
  }

  updatedSecondaryPlatforms(selectedPlatforms: string[]): boolean {
    return (
      this.difference(
        selectedPlatforms,
        this.preferredSecondaryPlatforms.map((platform) => platform.label)
      ).length > 0 ||
      this.difference(
        this.preferredSecondaryPlatforms.map((platform) => platform.label),
        selectedPlatforms
      ).length > 0
    );
  }

  addedSecondaryPlatforms(selectedPlatforms: string[]): string[] {
    return this.difference(
      selectedPlatforms,
      this.preferredSecondaryPlatforms.map((platform) => platform.label)
    );
  }

  removedSecondaryPlatforms(selectedPlatforms: string[]): string[] {
    return this.difference(
      this.preferredSecondaryPlatforms.map((platform) => platform.label),
      selectedPlatforms
    );
  }

  updatedPrimaryPlatform(selectedPlatform: string): boolean {
    return this.preferredPrimaryPlatforms.label !== selectedPlatform;
  }

  /** It will return all elements in `minuend` that aren't in `subtrahend` */
  private difference(minuend: string[], subtrahend: string[]): string[] {
    return minuend.filter((element) => !subtrahend.includes(element));
  }
}
export class CreatorWithProgamCodeProfile extends CreatorProfile {
  public readonly accounts: { type: string }[] = [];
  public readonly preferredPronouns: { label: string; value: string } = { label: "", value: "" };
  public readonly preferredPronoun: string = "";
  public readonly communicationPreferences: CommunicationPreferencesResponse = {} as CommunicationPreferencesResponse;

  constructor(
    creator: CreatorWithProgramCodeResponse,
    preferredPronouns?: Array<{ label: string; value: string }>,
    PreferredPronounOtherOption?: string
  ) {
    super(creator);
    this.accounts =
      creator.connectedAccounts?.filter((account: { disconnected: boolean }) => !account.disconnected) || [];
    this.communicationPreferences = creator.communicationPreferences;
    if (creator.accountInformation.preferredPronouns) {
      this.preferredPronouns =
        preferredPronouns?.find((pronoun) => pronoun.value === creator.accountInformation.preferredPronouns) ||
        ({ value: PreferredPronounOtherOption, label: PreferredPronounOtherOption } as {
          label: string;
          value: string;
        });
    } else {
      this.preferredPronouns =
        preferredPronouns && preferredPronouns.length > 0 ? preferredPronouns[0] : { label: "", value: "" };
    }
    this.preferredPronoun =
      this.preferredPronouns.value === PreferredPronounOtherOption ? creator.accountInformation.preferredPronouns : "";
  }

  socialAccountTypes(): string[] {
    return this.accounts.map((account) => account.type);
  }
}

export class CreatorWithpreferredPronouns {
  public readonly preferredPronouns: { label: string; value: string } = { label: "", value: "" };
  public readonly preferredPronoun: string = "";

  constructor(
    creator: CreatorWithProgramCodeResponse,
    preferredPronouns?: Array<{ label: string; value: string }>,
    PreferredPronounOtherOption?: string
  ) {
    if (creator.accountInformation.preferredPronouns) {
      this.preferredPronouns =
        preferredPronouns?.find((pronoun) => pronoun.value === creator.accountInformation.preferredPronouns) ||
        ({ value: PreferredPronounOtherOption, label: PreferredPronounOtherOption } as {
          label: string;
          value: string;
        });
    } else {
      this.preferredPronouns =
        preferredPronouns && preferredPronouns.length > 0 ? preferredPronouns[0] : { label: "", value: "" };
    }
    this.preferredPronoun =
      this.preferredPronouns.value === PreferredPronounOtherOption ? creator.accountInformation.preferredPronouns : "";
  }
}

class CreatorService {
  constructor(private readonly client: TraceableHttpClient) {}

  async getCreatorWithProgramCode(
    preferredPronouns?: { label: string; value: string }[],
    preferredPronounOtherOption?: string
  ): Promise<AxiosResponse<CreatorWithProgamCodeProfile>> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return this.client.get("/api/creators").then((response: any) => {
      response.data = new CreatorWithProgamCodeProfile(
        response.data as unknown as CreatorWithProgramCodeResponse,
        preferredPronouns,
        preferredPronounOtherOption
      );
      return response;
    });
  }

  async update(data: Partial<CreatorWithProgramCode>): Promise<AxiosResponse<void>> {
    return (await this.client.put(`/api/creators`, { body: data })) as AxiosResponse<void>;
  }

  async register(data: CreatorWithProgramCode): Promise<AxiosResponse<void>> {
    return (await this.client.post(`/api/creators`, { body: data })) as AxiosResponse<void>;
  }

  async updateProfilePicture(formData: FormData): Promise<AxiosResponse<void>> {
    return (await this.client.upload("/api/avatar", { body: formData })) as AxiosResponse<void>;
  }
}

export default CreatorService;
