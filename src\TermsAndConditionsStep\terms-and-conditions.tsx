import React, { memo, ReactElement, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import {
  ERROR,
  HAS_EXCEPTION,
  isObj,
  isString,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../utils";
import {
  AuthenticatedUser,
  BrowserAnalytics,
  CloseHandler,
  Dispatch,
  ErrorHandling,
  OnboardingStepConfiguration,
  State,
  ValidationError
} from "../types";
import { Controller, FieldError, useFormContext } from "react-hook-form";
import { Button, Checkbox, Input, Option, RadioButton, Select, Toast } from "@eait-playerexp-cn/core-ui-kit";
import { NextRouter } from "next/router";
import Form from "../utils/Form";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import TermsAndConditionsService, { SignerInformation } from "src/Browser/TermsAndConditionsService";
import CreatorForm, { CreatorFormRules } from "src/utils/CreatorForm";
import MigrationModal, { LayoutType } from "src/MigrationModal/MigrationModal";
import Footer from "src/common/Footer/Footer";
import CancelRegistrationModal from "src/common/CancelRegistrationModal/CancelRegistrationModal";
import { AxiosError, AxiosResponse } from "axios";
import { PageLabels } from "src/InformationStep/OnboardingInformationStep";
import { Country } from "@eait-playerexp-cn/metadata-types";
import CreatorWithProgramCode from "src/shared/creators/CreatorWithProgramCode";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { CreatorProfile, CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import {
  CreatorResponse,
  LegalInformationPayload,
  ProgramRequest,
  UpdateCreatorRequest
} from "@eait-playerexp-cn/creator-types";
import CreatorService from "@src/Browser/CreatorService";
import MailingAddress from "@src/shared/creators/MailingAddress";

export type TermsAndConditionLabels = {
  title: string;
  subTitleNewUser: string;
  enterDetails: string;
  entityType: string;
  entityIndividual: string;
  entityBusiness: string;
  contractTitle: string;
  contractDescription: string;
  declineModalDescription: string;
  declineModalHeader: string;
  decline: string;
  businessNameInputDesc: string;
  email: string;
  screenName: string;
  lastName: string;
  firstName: string;
  sameAddress: string;
  zip: string;
  state: string;
  country: string;
  city: string;
  street: string;
  alreadySigned: string;
  businessNameRequired: string;
  businessName: string;
  subTitleUpdatedTermsAndConditions: string;
  subTitleExistingUser: string;
  titleUpdatedTermsAndConditions: string;
};

type AddressFormData = {
  businessName: string | null;
  firstName: string;
  lastName: string;
  screenName: string;
  email: string;
  country: Country;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  entityType: string;
  sameAddress: boolean;
  tier?: string;
};

export type TermsAndConditionsLayoutProps = {
  buttons: {
    discard: string;
    save: string;
    cancel: string;
    next: string;
    close: string;
    declineTermsAndCondition: string;
    yes: string;
    no: string;
  };
};

type TermsAndConditionsFormProps = {
  submitTnCHandler: (data: AddressFormData, flag: boolean, navigateToPage?: string) => Promise<void>;
  termsAndConditionsLabels: TermsAndConditionLabels;
  legalEntity: LegalInformationPayload;
  rules: CreatorFormRules;
  mailingAddress: MailingAddress;
  countries: Country[];
  unRegistered: boolean;
  layout: TermsAndConditionsLayoutProps;
  onClose: () => void;
  isPending: boolean;
  navigateToPage?: string;
  setShowMigration: (show: boolean) => void;
  showMigration: boolean;
  stableDispatch: Dispatch;
  state: State;
  dispatch: Dispatch;
  router: NextRouter;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  configuration: TermsAndConditionsConfiguration;
};

const TermsAndConditionsForm = ({
  submitTnCHandler,
  termsAndConditionsLabels,
  breadcrumbLabels,
  legalEntity,
  rules,
  mailingAddress,
  countries,
  unRegistered,
  layout,
  onClose,
  isPending,
  navigateToPage,
  setShowMigration,
  showMigration,
  stableDispatch,
  state,
  dispatch,
  router,
  configuration
}: TermsAndConditionsFormProps) => {
  const { getValues, formState } = useFormContext();
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);
  const { userNavigated } = state;

  const onSave = () => submitTnCHandler(data as AddressFormData, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    if (stableDispatch) {
      stableDispatch({ type: USER_NAVIGATED, data: false });
    }
    if (navigateToPage) {
      router.push(navigateToPage);
    } else {
      router.push(configuration.navigateToPreviousPage);
    }
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      if (navigateToPage) {
        router.push(navigateToPage);
      } else {
        router.push(configuration.navigateToPreviousPage);
      }
      dispatch?.({ type: USER_NAVIGATED, data: false });
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  const buttons = useMemo(() => {
    const { cancel, discard, save } = layout.buttons;
    return { cancel, discard, save };
  }, [layout.buttons]);

  const migrationLayout = {
    buttons
  } as unknown as LayoutType;

  return (
    <>
      <PactEntityForm
        {...{
          termsAndConditionsLabels,
          legalEntity,
          rules,
          mailingAddress,
          countries,
          onClose,
          isPending,
          unRegistered,
          layout
        }}
      />
      <MigrationModal
        {...{ setShowMigration, showMigration, onSave, onDiscard, layout: migrationLayout, breadcrumbLabels }}
      />
    </>
  );
};

type RadioButtonOption = {
  label: string;
  value: string;
};

const EntityForm = ({ termsAndConditionsLabels }: { termsAndConditionsLabels: TermsAndConditionLabels }) => {
  const LegalEntityOptions = useMemo(() => {
    return [
      { value: "INDIVIDUAL", label: termsAndConditionsLabels.entityIndividual },
      { value: "BUSINESS", label: termsAndConditionsLabels.entityBusiness }
    ];
  }, [termsAndConditionsLabels.entityBusiness, termsAndConditionsLabels.entityIndividual]);
  const { control } = useFormContext();
  const updateEntityType = useCallback(({ value }: { value: string }, field: { onChange: (arg0: string) => void }) => {
    field.onChange(value);
  }, []);

  return (
    <section className="terms-condition-entity">
      <div className="fieldset">
        <legend>{termsAndConditionsLabels.entityType}</legend>
        <aside>
          <Controller
            control={control}
            name="entityType"
            render={({ field, fieldState: { error } }) => (
              <RadioButton
                errorMessage={error?.message ?? ""}
                {...field}
                selectedOption={{ label: "", value: field.value }}
                onChange={(item) => updateEntityType(item as RadioButtonOption, field)}
                options={LegalEntityOptions}
              />
            )}
          />
        </aside>
      </div>
    </section>
  );
};

const PersonalDetailsForm = ({
  termsAndConditionsLabels,
  rules
}: {
  termsAndConditionsLabels: TermsAndConditionLabels;
  rules: CreatorFormRules;
}) => {
  const { control, watch, unregister } = useFormContext();
  const entity = watch("entityType");
  useEffect(() => {
    if (entity === "INDIVIDUAL") {
      unregister("businessName", { keepDefaultValue: true });
    }
  }, [entity, unregister]);

  return (
    <section className="terms-condition-personal">
      {entity === "BUSINESS" && (
        <div className="personal-input-cont business">
          <Controller
            control={control}
            name="businessName"
            rules={rules.businessName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={error?.message ?? ""}
                {...field}
                label={termsAndConditionsLabels.businessName}
                placeholder={termsAndConditionsLabels.businessName}
                id="businessName"
                helpText={termsAndConditionsLabels.businessNameInputDesc}
              />
            )}
          />
        </div>
      )}
      <div className="personal-input-cont">
        <Controller
          control={control}
          name="firstName"
          rules={rules.firstName}
          render={({ field, fieldState: { error } }) => (
            <Input
              id={termsAndConditionsLabels.firstName}
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.firstName}
              placeholder={termsAndConditionsLabels.firstName}
            />
          )}
        />
        <Controller
          control={control}
          name="lastName"
          rules={rules.lastName}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              id={termsAndConditionsLabels.lastName}
              label={termsAndConditionsLabels.lastName}
              placeholder={termsAndConditionsLabels.lastName}
            />
          )}
        />
      </div>
      <div className="personal-input-cont">
        <Controller
          control={control}
          name="screenName"
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              id={termsAndConditionsLabels.screenName}
              label={termsAndConditionsLabels.screenName}
              placeholder={termsAndConditionsLabels.screenName}
              disabled
            />
          )}
        />
        <Controller
          control={control}
          name="email"
          rules={rules.email}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.email}
              id={termsAndConditionsLabels.email}
              placeholder={termsAndConditionsLabels.email}
            />
          )}
        />
      </div>
    </section>
  );
};

export type AddressFormProps = {
  termsAndConditionsLabels: TermsAndConditionLabels;
  legalEntity: LegalInformationPayload;
  rules: CreatorFormRules;
  mailingAddress: MailingAddress;
  countries: Country[];
  unRegistered: boolean;
};

const AddressForm = ({
  termsAndConditionsLabels,
  legalEntity,
  rules,
  mailingAddress,
  countries,
  unRegistered
}: AddressFormProps) => {
  const [asMailingAddress, setAsMailingAddress] = useState(
    unRegistered || checkSameAddress(mailingAddress, legalEntity)
  );
  const { register, control, setValue } = useFormContext();
  const sameAddress = asMailingAddress;
  const {
    city = "",
    country = { value: "", name: "", label: `Select ${termsAndConditionsLabels.country}` },
    state = "",
    street = "",
    zipCode = ""
  } = (sameAddress && mailingAddress) || legalEntity || {};

  const Country = useCallback(
    ({ error, field }: { error: FieldError | undefined; field: { onChange: (value: unknown) => void } }) =>
      (sameAddress && (
        <Input
          errorMessage={error?.message ?? ""}
          {...field}
          label={termsAndConditionsLabels.country}
          placeholder={termsAndConditionsLabels.country}
          disabled={sameAddress}
          id="country"
        />
      )) || (
        <Select
          id="terms-and-conditions"
          selectedOption={country as Option}
          errorMessage={error?.message ?? ""}
          {...field}
          onChange={(item) => {
            field.onChange(item);
          }}
          label={termsAndConditionsLabels.country}
          options={countries as unknown as Option[]}
          dark
        />
      ),
    [sameAddress, termsAndConditionsLabels.country, countries]
  );
  const sameAddressFld = register("sameAddress");
  const onSameAddressChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setAsMailingAddress(e.target.checked);
      sameAddressFld.onChange(e);
      setValue("sameAddress", e.target.checked);
      if (e.target.checked) {
        const {
          city = "",
          country = { value: "", name: "", label: "" },
          state = "",
          street = "",
          zipCode = ""
        } = mailingAddress;
        setValue("country", (isString(country) && country) || (isObj(country) && country.name));
        setValue("state", state);
        setValue("zipCode", zipCode);
        setValue("city", city);
        setValue("street", street);
      } else {
        const {
          city = "",
          country = { value: "", name: "", label: `Select ${termsAndConditionsLabels.country}` },
          state = "",
          street = "",
          zipCode = ""
        } = legalEntity;
        setValue("country", country);
        setValue("state", state);
        setValue("zipCode", zipCode);
        setValue("city", city);
        setValue("street", street);
      }
    },
    [legalEntity, sameAddressFld, setValue, mailingAddress]
  );

  return (
    <section className="terms-condition-address">
      <div className={`legal-entity-field ${asMailingAddress ? "asMailingAddress" : ""}`}>
        <Controller
          control={control}
          name="sameAddress"
          render={() => (
            <Checkbox
              options={[
                {
                  id: "sameAddress",
                  label: termsAndConditionsLabels.sameAddress,
                  isChecked: asMailingAddress,
                  onChange: onSameAddressChange,
                  dark: true
                }
              ]}
            />
          )}
        />
      </div>
      <div className="address-cont">
        <Controller
          control={control}
          name="street"
          rules={rules.street}
          defaultValue={street}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.street}
              placeholder={termsAndConditionsLabels.street}
              disabled={sameAddress}
              id="street"
            />
          )}
        />
        <Controller
          control={control}
          name="city"
          rules={rules.city}
          defaultValue={city}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.city}
              placeholder={termsAndConditionsLabels.city}
              disabled={sameAddress}
              id="city"
            />
          )}
        />
        <Controller
          control={control}
          key={`country-${!!sameAddress}`}
          name="country"
          rules={rules.country}
          defaultValue={sameAddress ? (isString(country) && country) || (isObj(country) && country.name) : country}
          render={({ field, fieldState: { error } }) => <Country {...{ error, field }} />}
        />
        <Controller
          control={control}
          name="state"
          rules={rules.state}
          defaultValue={state}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.state}
              placeholder={termsAndConditionsLabels.state}
              disabled={sameAddress}
              id="state"
            />
          )}
        />
        <Controller
          control={control}
          name="zipCode"
          rules={rules.zipCode}
          defaultValue={zipCode}
          render={({ field, fieldState: { error } }) => (
            <Input
              errorMessage={error?.message ?? ""}
              {...field}
              label={termsAndConditionsLabels.zip}
              placeholder={termsAndConditionsLabels.zip}
              disabled={sameAddress}
              id="zipCode"
            />
          )}
        />
      </div>
    </section>
  );
};

type FooterWrapperProps = {
  layout: TermsAndConditionsLayoutProps;
  onClose: () => void;
  isPending: boolean;
};

const FooterWrapper = memo(function FooterWrapper({ layout, onClose, isPending }: FooterWrapperProps) {
  const { watch } = useFormContext();
  const entity = watch("entityType");
  return (
    <Footer
      {...{
        buttons: layout.buttons,
        onCancel: onClose,
        disableSubmit: !entity || isPending,
        isPending
      }}
    />
  );
});

export type PactEntityFormProps = {
  termsAndConditionsLabels: TermsAndConditionLabels;
  legalEntity: LegalInformationPayload;
  rules: CreatorFormRules;
  mailingAddress: MailingAddress;
  countries: Country[];
  unRegistered: boolean;
  onClose: () => void;
  isPending: boolean;
  layout: TermsAndConditionsLayoutProps;
};

const PactEntityForm = memo(function PactEntityForm({
  termsAndConditionsLabels,
  legalEntity,
  rules,
  mailingAddress,
  countries,
  unRegistered,
  onClose,
  isPending,
  layout
}: PactEntityFormProps): JSX.Element {
  const { entityType = "" } = legalEntity || {};
  const { watch } = useFormContext();
  const entity = watch("entityType");
  return (
    <div className="pact-safe-entity-form">
      <EntityForm {...{ termsAndConditionsLabels, entityType }} />
      {entity && (
        <div className="personal-info-address-container">
          <PersonalDetailsForm {...{ termsAndConditionsLabels, rules }} />
          {legalEntity && (
            <AddressForm
              {...{ termsAndConditionsLabels, legalEntity, rules, mailingAddress, countries, unRegistered }}
            />
          )}
        </div>
      )}
      <FooterWrapper {...{ layout, onClose, isPending }} />
    </div>
  );
});

const checkSameAddress = (mailingAddress: MailingAddress, legalEntity: LegalInformationPayload) => {
  if (!legalEntity) {
    return false;
  }
  if (mailingAddress?.state !== legalEntity?.state) {
    return false;
  }
  if (mailingAddress?.city !== legalEntity?.city) {
    return false;
  }
  if (mailingAddress?.street !== legalEntity?.street) {
    return false;
  }
  if (mailingAddress?.zipCode !== legalEntity?.zipCode) {
    return false;
  }
  if (mailingAddress?.country && legalEntity?.country) {
    if (mailingAddress.country.name !== legalEntity.country.name) {
      return false;
    }
  } else {
    return false;
  }
  return true;
};

export type InformationLabels = {
  messages: {
    firstNameTooLong: string;
    lastNameTooLong: string;
    street: string;
    streetTooLong: string;
    city: string;
    cityTooLong: string;
    state: string;
    stateTooLong: string;
    zipCode: string;
    zipCodeTooLong: string;
    tShirtSize: string;
    entityType: string;
    businessName: string;
    businessNameTooLong: string;
    email: string;
    emailTooLong: string;
    emailInvalid: string;
    url: string;
    invalidUrl: string;
    followersMaxLength: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    dateOfBirthInvalid: string;
    ageMustBe18OrOlder: string;
    country: string;
  };
};

export type TermsAndConditionsConfiguration = OnboardingStepConfiguration & {
  navigateToNextPage: string;
  enableCommunicationPreferencesTab: boolean;
  navigateToPreviousPage: string;
};
export type TermsAndConditionsProps = {
  analytics: BrowserAnalytics;
  stableDispatch: Dispatch;
  navigateToPage: string;
  termsAndConditionsLabels: TermsAndConditionLabels;
  errorHandling: ErrorHandling;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  router: NextRouter;
  locale: string;
  configuration: TermsAndConditionsConfiguration;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  FLAG_COUNTRIES_BY_TYPE: boolean;
  isMounted: () => boolean;
  layout: TermsAndConditionsLayoutProps;
  setShowMigration: (value: boolean) => void;
  showMigration: boolean;
  dispatch: Dispatch;
  state: State;
  pageLabels: PageLabels & { infoLabels: InformationLabels } & { unhandledError: string };
  getIframe: (pactSafeUrl: string) => JSX.Element;
  PROGRAM_CODE: string;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  showPactSafeIframe: boolean;
  setShowPactSafeIframe: (value: boolean) => void;
  urlLocale: string;
  initialPage: string;
  user: AuthenticatedUser;
  opportunityId?: string | null;
};

export default memo(function TermsAndConditions({
  termsAndConditionsLabels,
  stableDispatch,
  analytics,
  errorHandling,
  layout,
  navigateToPage,
  showConfirmation,
  dispatch,
  setShowConfirmation,
  setShowMigration,
  showMigration,
  router,
  locale,
  onClose,
  breadcrumbLabels,
  FLAG_COUNTRIES_BY_TYPE,
  isMounted,
  errorToast,
  pageLabels,
  state,
  configuration,
  initialPage,
  getIframe,
  urlLocale,
  PROGRAM_CODE,
  user,
  showPactSafeIframe,
  setShowPactSafeIframe,
  opportunityId
}: TermsAndConditionsProps): JSX.Element {
  const [creator, setCreator] = useState<CreatorResponse | null>(null);
  const [countries, setCountries] = useState<Country[]>([]);
  const [pactSafeUrl, setPactSafeUrl] = useState("");
  const [showDeclineModal, setShowDeclineModal] = useState(false);
  const [showDeclineButton, setShowDeclineButton] = useState(true);
  const { isError = false, isValidationError = false, userNavigated } = state;
  const { unhandledError } = pageLabels;

  const creatorService = configuration.creatorsClient
    ? new CreatorsService(configuration.creatorsClient, configuration.DEFAULT_AVATAR_IMAGE)
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? new CreatorService(configuration.onBoardingClient)
    : undefined;
  const metadataService = new MetadataService(configuration.metadataClient);
  const termsAndConditionsService = new TermsAndConditionsService(configuration.onBoardingClient);

  const handleCancelRegistration = useCallback(() => {
    if (analytics.canceledOnboardingFlow) {
      analytics.canceledOnboardingFlow({ locale: router.locale ?? "", page: router.pathname });
    }
    router.push("/api/logout");
  }, [router]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const submitTnCHandler: any = useCallback(
    async (data: AddressFormData, navigateBack: string, navigateToPage: string) => {
      const {
        businessName = null,
        firstName,
        lastName,
        screenName,
        email,
        country,
        street,
        city,
        state,
        zipCode,
        entityType,
        sameAddress
      } = data;
      const { id: creatorId } = creator as CreatorResponse;
      const payload = {
        businessName,
        creatorId,
        locale,
        firstName,
        lastName,
        screenName,
        email,
        country: country.name || country
      };
      //eslint-disable-next-line @typescript-eslint/no-explicit-any
      let legalCountry: any = !legacyCreatorService ? { code: country.value, name: country.name } : country;
      legalCountry = sameAddress
        ? {
            value: mailingAddress?.country?.value,
            code: mailingAddress?.country?.value,
            name: mailingAddress?.country?.name,
            label: mailingAddress?.country?.name
          }
        : legalCountry;
      const legalEntity = { street, country: legalCountry, city, state, zipCode, businessName, entityType };
      const legalInformation = sameAddress
        ? { ...mailingAddress, country: legalCountry, businessName, entityType }
        : legalEntity;
      const communicationPreferences = { ...creator?.communicationPreferences, email };
      stableDispatch({ type: USER_NAVIGATED, data: false });

      try {
        stableDispatch({ type: LOADING, data: true });
        if (creator?.accountInformation) {
          creator.accountInformation.firstName = data.firstName;
          creator.accountInformation.lastName = data.lastName;
          const accountInformation = {
            ...creator.accountInformation,
            dateOfBirth: LocalizedDate.format(
              (creator.accountInformation.dateOfBirth as unknown as LocalizedDate).toDate(),
              "YYYY-MM-DD"
            )
          };

          const requestPayload = {
            legalInformation,
            accountInformation,
            creatorConnectedProgram: PROGRAM_CODE
          } as unknown as Partial<CreatorResponse>;

          const payload = configuration.enableCommunicationPreferencesTab
            ? ({ ...requestPayload, communicationPreferences } as unknown as Partial<CreatorResponse>)
            : requestPayload;
          if (legacyCreatorService) await legacyCreatorService.update(payload as Partial<CreatorWithProgramCode>);
          else
            await (creatorService as CreatorsService).updateCreator({
              ...payload,
              program: { code: PROGRAM_CODE } as unknown as ProgramRequest
            } as unknown as UpdateCreatorRequest);
        }
        if (navigateToPage) {
          router.push(navigateToPage);
        } else if (navigateBack) {
          router.push(configuration.navigateToPreviousPage);
        } else {
          const result = (await termsAndConditionsService.getSigningUrl({
            ...payload,
            businessName: payload?.businessName?.trim() || null,
            program: PROGRAM_CODE
          } as SignerInformation)) as AxiosResponse;
          stableDispatch({ type: LOADING, data: false });
          setPactSafeUrl(result.data.contractUrl);
          setShowPactSafeIframe(true);
          stableDispatch({ type: LOADING, data: false });
        }
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    },
    [creator, locale, stableDispatch]
  );
  const { pending: isPending, execute: submitTnCHandlerClb } = useAsync(submitTnCHandler, false);

  const handleIframeMessage = useCallback(
    async ({ data: { event: userAction } }: { data: { event: string } }, creator: CreatorProfile | CreatorResponse) => {
      if (userAction === "request_signed" && creator) {
        if (analytics.signedTermsAndConditions) {
          analytics.signedTermsAndConditions({ locale: router.locale as string, accepted: true });
        }
        stableDispatch({ type: LOADING, data: true });
        setShowDeclineButton(false);
        let redirectUrl;
        if (legacyCreatorService) {
          redirectUrl =
            (creator as unknown as CreatorWithProgramCode).accountInformation?.status === "UNREGISTERED"
              ? `${urlLocale}${configuration.navigateToNextPage}`
              : user?.programs?.includes(PROGRAM_CODE)
                ? `${urlLocale}/dashboard`
                : `${urlLocale}${configuration.navigateToNextPage}`;
        } else {
          if ((creator as unknown as CreatorProfile).program?.status === "INACTIVE") {
            await (creatorService as CreatorsService).updateCreator({
              program: { code: PROGRAM_CODE, status: "ACTIVE" } as unknown as ProgramRequest
            } as unknown as UpdateCreatorRequest);
            redirectUrl = `${urlLocale}dashboard`;
          } else {
            redirectUrl =
              (creator as unknown as CreatorProfile).program?.status === "UNREGISTERED"
                ? `${urlLocale}${configuration.navigateToNextPage}`
                : user?.programs?.includes(PROGRAM_CODE)
                  ? `${urlLocale}dashboard`
                  : `${urlLocale}${configuration.navigateToNextPage}`;
          }
        }

        try {
          await termsAndConditionsService.clearSignedStatusForProgram(PROGRAM_CODE);
          if (opportunityId) {
            if (redirectUrl === `${urlLocale}signup-complete`) {
              if (analytics.completedOnboardingFlow)
                analytics.completedOnboardingFlow({ locale: router.locale as string });
            }
            redirectUrl = `${urlLocale}opportunities/${opportunityId}`;
          }
          if (initialPage) {
            redirectUrl = initialPage;
          }
          router.push(redirectUrl);
        } catch (e) {
          stableDispatch({ type: LOADING, data: false });
          errorHandling(stableDispatch, e as Error | AxiosError);
        } finally {
          stableDispatch({ type: LOADING, data: false });
        }
      } else if (userAction === "canceled") {
        if (analytics.signedTermsAndConditions) {
          analytics.signedTermsAndConditions({ locale: router.locale as string, accepted: false });
        }
        setShowPactSafeIframe(false);
        location.reload(); //reload
      }
    },
    [router, creator, stableDispatch]
  );

  useEffect(() => {
    async function onMount() {
      try {
        stableDispatch({ type: LOADING, data: true });

        const creator = legacyCreatorService
          ? (await legacyCreatorService.getCreatorWithProgramCode()).data
          : await creatorService?.getCreator(PROGRAM_CODE);

        if (isMounted()) {
          setCreator(creator as unknown as CreatorResponse);
          stableDispatch({ type: LOADING, data: false });
        }
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as Error | AxiosError);
      }
    }
    onMount();
    return () => {
      setShowPactSafeIframe(false);
    };
  }, [isMounted, stableDispatch]);

  useEffect(() => {
    const countries = FLAG_COUNTRIES_BY_TYPE ? metadataService.getCountriesMatching() : metadataService.getCountries();
    countries
      .then((res) => {
        if (isMounted()) {
          setCountries(res);
        }
      })
      .catch((e) => {
        stableDispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
        errorHandling(stableDispatch, e);
      });
  }, [isMounted, stableDispatch]);

  useEffect(() => {
    if (creator) {
      const handler = (e: { data: { event: string } }) => handleIframeMessage(e, creator);
      window.addEventListener("message", handler);
      return () => {
        window.removeEventListener("message", handler);
      };
    }
  }, [creator, handleIframeMessage]);

  useEffect(() => {
    if (userNavigated && showPactSafeIframe) {
      router.push(configuration.navigateToPreviousPage);
      if (dispatch) {
        dispatch({ type: USER_NAVIGATED, data: false });
      }
    }
  }, [router, userNavigated]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError || toastContent(isValidationError as unknown as ValidationError[])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const { confirmationDesc1, confirmationDesc2, modalConfirmationTitle, infoLabels } = pageLabels;

  const {
    title,
    titleUpdatedTermsAndConditions,
    subTitleNewUser,
    subTitleExistingUser,
    subTitleUpdatedTermsAndConditions,
    decline,
    declineModalDescription,
    declineModalHeader
  } = termsAndConditionsLabels;
  let legalEntity = !legacyCreatorService
    ? creator?.legalInformation
    : (creator as unknown as CreatorWithProgramCode)?.legalEntity;
  const userStatus: string = !legacyCreatorService
    ? ((creator as unknown as CreatorProfile)?.program?.status as string)
    : ((creator as unknown as CreatorWithProgramCode)?.accountInformation?.status as string);

  if (!legacyCreatorService && !legalEntity) {
    legalEntity = {
      entityType: "",
      businessName: "",
      street: "",
      country: { value: "", name: "", label: "" },
      city: "",
      state: "",
      zipCode: ""
    };
  }
  const mailingAddress = !legacyCreatorService
    ? ({
        ...creator?.mailingAddress,
        country: {
          ...creator?.mailingAddress?.country,
          code: (creator?.mailingAddress?.country as unknown as { value: string })?.value
        }
      } as unknown as MailingAddress)
    : (creator?.mailingAddress as unknown as MailingAddress);
  const unRegistered = ["UNREGISTERED"].includes(userStatus);
  const inActive = ["INACTIVE"].includes(userStatus);
  const defaultValues = useMemo(() => {
    if (creator) {
      const {
        accountInformation: { firstName, lastName, defaultGamerTag: screenName },
        communicationPreferences: { email }
      } = creator;
      const entityType = legalEntity?.entityType || "";
      const businessName = legalEntity?.businessName || "";
      return {
        sameAddress: unRegistered || checkSameAddress(mailingAddress, creator.legalInformation),
        entityType,
        businessName,
        firstName,
        lastName,
        email,
        screenName
      };
    }
    return creator;
  }, [creator, unRegistered]);

  const rules = useMemo(() => CreatorForm.rules(infoLabels), [infoLabels]);

  const declineTermsAndCondition = () => {
    setShowDeclineModal(true);
  };

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: layout.buttons.yes,
    no: layout.buttons.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  const declineTermsAndConditionLabels = {
    title: declineModalHeader,
    yes: layout.buttons.declineTermsAndCondition,
    no: layout.buttons.cancel,
    close: layout.buttons.close,
    confirmationDesc1: declineModalDescription
  };

  const handleModalClose = useCallback(() => {
    setShowConfirmation(false);
    setShowDeclineModal(false);
  }, []);
  return (
    <section className="terms-and-conditions">
      <main>
        <header>
          <h3 className="terms-and-condition-title">
            {userStatus && ((!unRegistered && !inActive && titleUpdatedTermsAndConditions) || title)}
          </h3>
          <p className="terms-and-conditions-description">
            {userStatus &&
              ((unRegistered && subTitleNewUser) ||
                (inActive && subTitleExistingUser) ||
                ((!unRegistered || !inActive) && subTitleUpdatedTermsAndConditions))}
          </p>
        </header>
        <section className="pactsafe">
          {(showPactSafeIframe && (
            <div className="pactSafe-iframe-cont">
              {" "}
              <hr />
              {getIframe(pactSafeUrl)}
              {showDeclineButton && (
                <Button variant="tertiary" dark size="md" onClick={declineTermsAndCondition}>
                  {decline}
                </Button>
              )}
            </div>
          )) ||
            (defaultValues && rules && (
              <Form
                mode="onChange"
                onSubmit={submitTnCHandlerClb}
                key="Pact-Safe"
                revalidate="onChange"
                {...{ defaultValues }}
              >
                <TermsAndConditionsForm
                  {...{
                    state,
                    dispatch,
                    breadcrumbLabels,
                    submitTnCHandler,
                    termsAndConditionsLabels,
                    legalEntity: legalEntity as LegalInformationPayload,
                    rules,
                    mailingAddress,
                    countries,
                    unRegistered,
                    layout,
                    onClose,
                    isPending,
                    navigateToPage,
                    setShowMigration,
                    showMigration,
                    stableDispatch,
                    router,
                    configuration
                  }}
                />
              </Form>
            ))}
        </section>
      </main>
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
      {showDeclineModal && (
        <CancelRegistrationModal
          {...{
            labels: declineTermsAndConditionLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </section>
  );
});
