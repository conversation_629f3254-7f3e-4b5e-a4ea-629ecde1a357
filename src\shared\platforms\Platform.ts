export type PlatformResponse = { id: string; name: string; boxArtUrl: string; type: string };

export default class Platform {
  public imageAsIcon: string;

  static fromApi(data: PlatformResponse): Platform {
    return new Platform(data.id, data.name, data.boxArtUrl, data.type);
  }

  constructor(
    readonly value: string,
    readonly label: string,
    boxArtUrl?: string,
    readonly type?: string
  ) {
    this.imageAsIcon = boxArtUrl as string;
  }

  isPrimary(): boolean {
    return this.type === "PRIMARY";
  }
}
