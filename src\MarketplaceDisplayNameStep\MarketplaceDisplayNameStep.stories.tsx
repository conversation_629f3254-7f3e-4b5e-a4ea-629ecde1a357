import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import MarketplaceDisplayNameStepForm, { MarketDisplayNamePageLabels } from "./MarketplaceDisplayNameStep";

const meta: Meta<typeof MarketplaceDisplayNameStepForm> = {
  title: "Component Library/Marketplace Display Name Step",
  component: MarketplaceDisplayNameStepForm,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof MarketplaceDisplayNameStepForm>;

const mockDispatch = (): void => {};
const mockErrorHandling = (): void => {};

export const MarketplaceDisplayNameStepFormComponent: Story = {
  args: {
    labels: {
      pageLabels: {
        title: "Setup Your Public Display Name",
        description:
          "Create a public display name that will be shown to customers in the marketplace. This name will help identify your brand and make it easy for customers to find and recognize your content. Choose a name that reflects your brand identity.",
        nameRequirements:
          "Public display names can only contain letters and numbers, up to a maximum of 22 characters, with no special symbols",
        publicDisplayName: "Create Public Display Name",
        confirmPublicDisplayName: "Confirm Public Display Name",
        understandingCheckbox: "I understand that once set, my display name cannot be changed.",
        next: "Next",
        cancel: "Cancel",
        displayNote: "This name is displayed on the marketplace to the public."
      } as MarketDisplayNamePageLabels,
      infoLabels: {
        publicDisplayNameRequired: "Public display name is required",
        confirmNameRequired: "Confirm public display name is required",
        tooLong: "Display names can be up to 22 characters long",
        specialCharactersNotAllowed: "Only letters and numbers are allowed. No special characters or spaces.",
        doNotMatch: "Display names do not match. Please try again.",
        understandingRequired: "You must acknowledge that the display name cannot be changed.",
        alreadyInUse: "This display name is already in use. Please choose a different one.",
        inappropriateLanguage: "The display name contains inappropriate language. Please choose a different one."
      }
    },
    dispatch: mockDispatch,
    errorHandling: mockErrorHandling,
    onClose: () => console.log("Close clicked"),
    onStepNavigation: () => console.log("Step navigation triggered"),
    backgroundImage: "/img/union.svg"
  }
};
