import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import CreatorCodeForm from "./CreatorCodeForm";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { NextRouter } from "next/router";
import { CloseHand<PERSON>, Dispatch, ErrorHandling } from "../types";
import { creatorCodeFormLabels, creatorCodeInfoLabels } from "../Translations/CreatorCodeForm";

// Mock dependencies
jest.mock("../Browser/ValidateCreatorCodeService");
jest.mock("next/router");

describe("CreatorCodeForm", () => {
  const creatorCodeFormProps = {
    pageLabels: creatorCodeFormLabels,
    infoLabels: creatorCodeInfoLabels,
    stableDispatch: (() => {}) as Dispatch,
    configuration: {
      onBoardingClient: {} as TraceableHttpClient,
      metadataClient: {} as TraceableHttpClient,
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png"
    },
    errorHandling: (() => {}) as ErrorHandling,
    layout: {
      buttons: {
        yes: "Yes",
        close: "Close",
        no: "No"
      }
    },
    infoUnionPath: "/img/union.svg",
    router: {} as NextRouter,
    onClose: (() => {}) as React.MouseEventHandler<HTMLButtonElement>,
    showConfirmation: false,
    setShowConfirmation: (() => {}) as (show: boolean) => void,
    state: {},
    PROGRAM_CODE: "test_program_code",
    errorToast: (() => {}) as (JSXElementConstructor: React.ReactElement, CloseHandler: CloseHandler) => void,
    unhandledError: "Unexpected error"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows form elements to chose a creator code", () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    expect(screen.getByText(creatorCodeFormLabels.title)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.description)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.tipsTitle)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.tip1)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.tip2)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.codeRequirements)).toBeInTheDocument();
    expect(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode)).toBeInTheDocument();
    expect(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.next)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.cancel)).toBeInTheDocument();
    expect(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode)).toHaveAttribute(
      "placeholder",
      creatorCodeFormLabels.customizeYourCode
    );
    expect(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode)).toHaveAttribute(
      "placeholder",
      creatorCodeFormLabels.confirmYourCode
    );
    expect(screen.getByText(creatorCodeFormLabels.next)).toBeDisabled();
  });

  it("shows validation error when codes do not match", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "TestCode1");
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode), "TestCode2");
    await userEvent.click(screen.getByText(creatorCodeFormLabels.next));

    expect(await screen.findByText(creatorCodeInfoLabels.doNotMatch)).toBeInTheDocument();
  });

  it("validates maximum length of creator code", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);
    const longCode = "a".repeat(25); // Exceeds 22 character limit

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), longCode);

    expect(await screen.findByText(creatorCodeInfoLabels.tooLong)).toBeInTheDocument();
  });

  it("validates special characters not allowed", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "Test@Code!");

    expect(await screen.findByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).toBeInTheDocument();
  });

  it("calls its on close callback when cancel button is clicked", async () => {
    const onClose = jest.fn();
    const propsWithOnClose = {
      ...creatorCodeFormProps,
      onClose: onClose
    };
    render(<CreatorCodeForm {...propsWithOnClose} />);

    await userEvent.click(screen.getByText(creatorCodeFormLabels.cancel));

    expect(onClose).toHaveBeenCalled();
  });

  it("doesn't show error messages for valid creator code", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    // Test matching valid alphanumeric codes
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "ValidCode123");
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode), "ValidCode123");

    expect(await screen.queryByText(creatorCodeInfoLabels.doNotMatch)).not.toBeInTheDocument();
    expect(screen.queryByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).not.toBeInTheDocument();
  });

  it("shows cancel registration modal when showConfirmation is true", () => {
    const propsWithConfirmation = {
      ...creatorCodeFormProps,
      showConfirmation: true
    };
    render(<CreatorCodeForm {...propsWithConfirmation} />);

    expect(screen.getByText(creatorCodeFormLabels.modalConfirmationTitle)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.confirmationDesc1)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.yes)).toBeInTheDocument();
    expect(screen.getByText(creatorCodeFormLabels.no)).toBeInTheDocument();
  });

  it("navigates to logout when user confirms cancellation", async () => {
    const router = { push: jest.fn() };
    const propsWithConfirmation = {
      ...creatorCodeFormProps,
      router: router as unknown as NextRouter,
      showConfirmation: true
    };
    render(<CreatorCodeForm {...propsWithConfirmation} />);

    await userEvent.click(screen.getByText(creatorCodeFormLabels.yes));

    expect(router.push).toHaveBeenCalledWith("/api/logout");
    expect(router.push).toHaveBeenCalledTimes(1);
  });

  it("closes modal when user clicks No in cancel registration modal", async () => {
    const setShowConfirmation = jest.fn();
    const propsWithConfirmation = {
      ...creatorCodeFormProps,
      showConfirmation: true,
      setShowConfirmation: setShowConfirmation
    };
    render(<CreatorCodeForm {...propsWithConfirmation} />);

    await userEvent.click(screen.getByText(creatorCodeFormLabels.no));

    expect(setShowConfirmation).toHaveBeenCalledWith(false);
  });

  it("disables Next button when form has validation errors", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "Test@Code!"); // Invalid characters

    expect(screen.getByText(creatorCodeFormLabels.next)).toBeDisabled();
  });

  it("enables Next button when form is valid", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "ValidCode123");
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode), "ValidCode123");

    expect(await screen.getByText(creatorCodeFormLabels.next)).not.toBeDisabled();
  });

  it("accepts exactly 20 characters (maximum allowed)", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "a".repeat(20));

    expect(await screen.queryByText(creatorCodeInfoLabels.tooLong)).not.toBeInTheDocument();
  });

  it("disables Next button when confirm code field is empty but customize is filled", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "TestCode");

    expect(screen.getByText(creatorCodeFormLabels.next)).toBeDisabled();
  });

  it("clears validation error when fixing invalid characters", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "Test@Code!");
    expect(await screen.findByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).toBeInTheDocument();

    await userEvent.clear(screen.getByRole("textbox", { name: /customize your code/i }));
    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "ValidCode123");

    expect(await screen.queryByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).not.toBeInTheDocument();
  });

  it("clears validation error when fixing too long code", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "a".repeat(25));
    expect(await screen.findByText(creatorCodeInfoLabels.tooLong)).toBeInTheDocument();

    await userEvent.clear(screen.getByRole("textbox", { name: /customize your code/i }));
    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "ValidCode");

    expect(await screen.queryByText(creatorCodeInfoLabels.tooLong)).not.toBeInTheDocument();
  });

  it("accepts empty string without validation error initially", () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    expect(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode)).toHaveValue("");
    expect(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode)).toHaveValue("");
    expect(screen.queryByText(creatorCodeInfoLabels.customizeCodeRequired)).not.toBeInTheDocument();
    expect(screen.queryByText(creatorCodeInfoLabels.confirmCodeRequired)).not.toBeInTheDocument();
  });

  it("validates confirm field when customize field is updated with matching value", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "Code1");
    await userEvent.type(screen.getByRole("textbox", { name: /confirm your code/i }), "Code2");

    expect(await screen.findByText(creatorCodeInfoLabels.doNotMatch)).toBeInTheDocument();

    // Clear and update customize field to match confirm field
    await userEvent.clear(screen.getByRole("textbox", { name: /customize your code/i }));
    await userEvent.type(screen.getByRole("textbox", { name: /customize your code/i }), "Code2");

    expect(await screen.queryByText(creatorCodeInfoLabels.doNotMatch)).not.toBeInTheDocument();
  });

  it("closes modal when clicking close button in cancel registration modal", async () => {
    const setShowConfirmation = jest.fn();
    const propsWithConfirmation = {
      ...creatorCodeFormProps,
      showConfirmation: true,
      setShowConfirmation: setShowConfirmation
    };
    render(<CreatorCodeForm {...propsWithConfirmation} />);

    await userEvent.click(screen.getByLabelText("Close"));

    expect(setShowConfirmation).toHaveBeenCalledWith(false);
  });

  it("prevents form submission when Next button is disabled", async () => {
    const submitHandler = jest.fn();
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "Test@Code!");
    expect(screen.getByText(creatorCodeFormLabels.next)).toBeDisabled();

    await userEvent.click(screen.getByText(creatorCodeFormLabels.next));
    expect(submitHandler).not.toHaveBeenCalled();
  });

  it("accepts mixed case alphanumeric codes", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "Test123Code");
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode), "Test123Code");

    await waitFor(() => {
      expect(screen.queryByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).not.toBeInTheDocument();
      expect(screen.queryByText(creatorCodeInfoLabels.doNotMatch)).not.toBeInTheDocument();
    });
  });

  it("rejects codes with various special characters (spaces, hyphens, underscores)", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    const customizeInput = screen.getByRole("textbox", { name: /customize your code/i });
    const invalidCodes = [
      "Test Code", // space
      "Test-Code", // hyphen
      "Test_Code" // underscore
    ];

    for (const code of invalidCodes) {
      await userEvent.clear(customizeInput);
      await userEvent.type(customizeInput, code);
      expect(await screen.findByText(creatorCodeInfoLabels.specialCharactersNotAllowed)).toBeInTheDocument();
    }
  });

  it("validates correctly regardless of field interaction order", async () => {
    render(<CreatorCodeForm {...creatorCodeFormProps} />);

    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.confirmYourCode), "ValidCode123");
    await userEvent.type(screen.getByLabelText(creatorCodeFormLabels.customizeYourCode), "ValidCode123");

    await waitFor(() => {
      expect(screen.queryByText(creatorCodeInfoLabels.doNotMatch)).not.toBeInTheDocument();
      expect(screen.getByText(creatorCodeFormLabels.next)).not.toBeDisabled();
    });
  });
});
