import { Factory } from "fishery";
import Random from "../Random";
import ConnectedAccount from "../../shared/channels/ConnectedAccount";

const connectedAccountFactory = Factory.define(() => {
  return {
    name: Random.firstName(),
    disconnected: false,
    username: Random.userName(),
    id: Random.uuid(),
    type: Random.accountType(),
    uri: Random.url(),
    thumbnail: Random.imageUrl(),
    isExpired: true,
    accountId: Random.uuid()
  };
});

export function aConnectedAccount(override = {}): ConnectedAccount {
  return connectedAccountFactory.build(override) as ConnectedAccount;
}

const factory = Factory.define(() => [aConnectedAccount(), aConnectedAccount(), aConnectedAccount()]);

export function aConnectedAccounts(override = []): Array<ConnectedAccount> {
  return factory.build(override) as Array<ConnectedAccount>;
}
