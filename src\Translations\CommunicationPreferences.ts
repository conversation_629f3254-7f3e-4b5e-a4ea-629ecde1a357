export const CommunicationPreferencesLabels = {
  title: "Connect Account",
  myAccount: "My Account",
  removeAccount: "Remove Account",
  expireAccount: "Expire Account",
  or: "or",
  reconnectAccount: "Reconnect Account",
  close: "Close",
  addAccount: "Add Account",
  connectAnAccount: "Connect an Account",
  modal: {
    removeAccountTitle: "Remove Account Title",
    removeAccountDescription1: "Remove Account Description 1",
    removeAccountDescription2: "Remove Account Description 2"
  },
  remove: "Remove",
  cancel: "Cancel",
  removeAccountDescription1: "Remove Account Description 1",
  removeAccountDescription2: "Remove Account Description 2",
  communicationSettings: "Communication Settings",
  discord: "Discord",
  email: "Email",
  verificationPending: "Verification Pending",
  reVerifyAccount: "Re-verify Account",
  connect: "Connect",
  save: "Save",
  discard: "Discard",
  layout: {
    main: {
      unhandledError: "Unhandled Error"
    },
    buttons: {
      yes: "Yes",
      no: "No",
      close: "Close",
      cancel: "Cancel Onboarding",
      discard: "Discard",
      save: "Save",
      next: "Next"
    }
  },
  translation: {
    messages: {
      preferredEmailTooLong: "Preferred email is too long",
      preferredEmailInvalid: "Preferred email is invalid",
      preferredPhoneNumber: "Preferred phone number is invalid",
      preferredPhoneNumberTooLong: "Preferred phone number is too long",
      contentLanguage: "Content language is required",
      language: "Language is required",
      preferredEmail: "Preferred email is required"
    },
    labels: {
      preferredEmail: "Preferred Email",
      preferredEmailAddressTitle: "Preferred Email Address",
      preferredEmailAddressDescription: "Please enter your preferred email address.",
      preferredPhoneNumberTitle: "Add Your Preferred Phone Number",
      preferredPhoneNumberDescription: "Please enter your preferred phone number.",
      preferredPhoneNumber: "Preferred Phone Number",
      contentLanguageTitle: "Content Language",
      contentLanguage: "Content Language",
      languageTitle: "Please select your Language",
      contentLanguagesDescription: "Please select your preferred content languages.",
      language: "Language",
      languageDescription: "Please select your preferred language.",
      contentLanguagesTitle: "Content Languages",
      discordTitle: "Discord",
      discordDescription: "Please enter your Discord username.",
      addDiscord: "Add Discord"
    },
    confirmationDesc1: "Confirmation Description 1",
    confirmationDesc2: "Confirmation Description 2",
    modalConfirmationTitle: "Modal Confirmation Title",
    title: "Communication Preferences",
    description: "Please select your preferred communication methods."
  },
  breadcrumbLabels: {
    modalTitle: "Modal Title",
    modalMessage: "Modal Message"
  }
};
