import { User } from "@eait-playerexp-cn/server-kernel";

export type AccountInformationFormValues = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  nucleusId: number;
  originEmail: string;
  status: string;
  isPayable: boolean;
  creatorCode: string;
  preferredName: string;
  preferredPronouns: string;
};

export default class AccountInformationInput {
  readonly firstName: string;
  readonly lastName: string;
  readonly dateOfBirth: string;
  readonly nucleusId: number;
  readonly originEmail: string;
  readonly defaultGamerTag: string;
  readonly status: string | null;
  readonly isPayable: boolean;
  readonly creatorCode: string;
  readonly preferredName: string;
  readonly preferredPronouns: string;

  constructor(creator: User, values: AccountInformationFormValues) {
    this.firstName = values.firstName;
    this.lastName = values.lastName;
    this.dateOfBirth = values.dateOfBirth;
    this.nucleusId = values.nucleusId;
    this.originEmail = values.originEmail;
    this.defaultGamerTag = creator.username;
    this.status = creator.id === null ? "UNREGISTERED" : values.status || null;
    this.isPayable = values.isPayable;
    this.creatorCode = values.creatorCode;
    this.preferredName = values.preferredName;
    this.preferredPronouns = values.preferredPronouns;
  }
}
