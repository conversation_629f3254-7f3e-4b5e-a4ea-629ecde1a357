export type PreferredPlatformResponse = { id: string; name: string; code: string; type: string };

export default class PreferredPlatform {
  static fromApi(data: PreferredPlatformResponse): PreferredPlatform {
    return new PreferredPlatform(data.id, data.name, data.type);
  }

  constructor(
    readonly value: string,
    readonly label: string,
    readonly type?: string
  ) {}

  isPrimary(): boolean {
    return this.type === "PRIMARY";
  }
}
