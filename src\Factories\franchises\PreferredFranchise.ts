import { Factory } from "fishery";
import Random from "../Random";
import { PreferredFranchiseResponse } from "src/shared/franchises/PreferredFranchise";

const factory = Factory.define<PreferredFranchiseResponse>(() => {
  const name = Random.franchise();
  return {
    name,
    id: Random.uuid(),
    type: Random.franchiseType(),
    code: name.toLowerCase()
  };
});

export function aPrimaryFranchise(override = {}): PreferredFranchiseResponse {
  return factory.build({ type: "PRIMARY", ...override });
}

export function aSecondaryFranchise(override = {}): PreferredFranchiseResponse {
  return factory.build({ type: "SECONDARY", ...override });
}
