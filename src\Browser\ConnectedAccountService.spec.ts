import type { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import ConnectedAccountsService, { ErrorType, FacebookPage } from "./ConnectedAccountService";
import { ConnectedAccount } from "../types";
import Random from "../../src/Factories/Random";

jest.mock("@eait-playerexp-cn/http-client");

describe("ConnectedAccountsService", () => {
  it("shows clear account type", async () => {
    const client = { delete: jest.fn().mockResolvedValue({}) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    await service.clearAccountType();

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith("/api/account-types");
  });

  it("shows clear Facebook pages", async () => {
    const client = { delete: jest.fn().mockResolvedValue({}) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    await service.clearFbPages();

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith("/api/facebook-connect");
  });

  it("shows connect Facebook pages", async () => {
    const client = { post: jest.fn() } as unknown as TraceableHttpClient;
    const selectedPage = { pageId: Random.uuid(), pageAccessToken: Random.string() };
    const service = new ConnectedAccountsService(client);

    await service.connectFbPages(selectedPage);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/api/facebook-channels", { body: selectedPage });
  });

  it("shows get connected accounts", async () => {
    const nucleusId = Random.nucleusId();
    const connectedAccounts: Array<ConnectedAccount> = [
      {
        name: Random.firstName(),
        disconnected: false,
        username: Random.userName(),
        id: Random.uuid(),
        type: "FACEBOOK",
        uri: Random.url(),
        thumbnail: Random.imageUrl(),
        isExpired: true,
        accountId: Random.uuid()
      }
    ];
    const client = { get: jest.fn().mockResolvedValue({ data: connectedAccounts }) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    const result = await service.getConnectedAccounts(nucleusId);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/api/connected-accounts", { query: { nucleusId } });
    expect(result).toEqual(connectedAccounts);
  });

  it("shows get all connected accounts with expiration status", async () => {
    const nucleusId = Random.nucleusId();
    const connectedAccounts: Array<ConnectedAccount> = [];
    const client = { get: jest.fn().mockResolvedValue({ data: connectedAccounts }) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    const result = await service.getAllConnectedAccountsWithExpirationStatus(nucleusId);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/api/v2/connected-accounts", { query: { nucleusId } });
    expect(result).toEqual(connectedAccounts);
  });

  it("shows get Facebook pages", async () => {
    const pages: FacebookPage[] = [];
    const client = { get: jest.fn().mockResolvedValue({ data: { pages } }) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    const result = await service.getFacebookPages();

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/api/facebook-pages");
    expect(result).toEqual({ pages });
  });

  it("shows remove connected account", async () => {
    const accountId = "123";
    const client = { delete: jest.fn().mockResolvedValue({}) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    await service.removeConnectedAccount(accountId);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(`/api/accounts/${accountId}`);
  });

  it("shows remove discord connected account", async () => {
    const accountId = "123";
    const client = { delete: jest.fn().mockResolvedValue({}) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    await service.removeDiscordAccount(accountId);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(`/api/discord-accounts/${accountId}`);
  });

  it("shows get connect account errors", async () => {
    const error: ErrorType = { code: "error_code", message: "error_message" };
    const client = { get: jest.fn().mockResolvedValue({ data: error }) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    const result = await service.getConnectAccountErrors();

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/api/errors");
    expect(result).toEqual(error);
  });

  it("shows delete connected account errors", async () => {
    const error: ErrorType = { code: "error_code", message: "error_message" };
    const client = { delete: jest.fn().mockResolvedValue({ data: error }) } as unknown as TraceableHttpClient;
    const service = new ConnectedAccountsService(client);

    const result = await service.deleteConnectedAccountErrors();

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith("/api/sessions/error");
    expect(result).toEqual(error);
  });
});
