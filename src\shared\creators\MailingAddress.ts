import { MailingAddressPayload } from "@eait-playerexp-cn/creator-types";
import Country from "../countries/Country";

export default class MailingAddress {
  static fromApi(data: MailingAddressPayload): MailingAddress {
    return new MailingAddress(
      data.state,
      data.street,
      data.city,
      data.zipCode,
      data.country ? Country.fromApi(data.country) : undefined
    );
  }

  constructor(
    readonly state: string,
    readonly street: string,
    readonly city: string,
    readonly zipCode: string,
    readonly country?: Country
  ) {}
}
