.update-profile-picture-container {
  @apply flex flex-col md:flex-row;
}
.update-profile-picture-placeholder {
  @apply relative flex items-center justify-center;
}
.update-profile-picture-placeholder > .update-profile-picture-profile-card-logo {
  @apply relative top-meas0  h-[100px] w-[100px] overflow-hidden rounded-full border border-[#2B398E] bg-section-background-1 p-[2px];
}
.update-profile-picture-placeholder > .update-profile-picture-profile-card-logo-edit {
  @apply absolute -bottom-meas6 flex h-meas16 w-meas16 cursor-pointer items-center justify-center rounded-full bg-indigo-50 text-white;
}
.update-profile-picture-upload-section {
  @apply order-2 flex h-auto w-full flex-col items-center md:order-1 md:mr-meas12 md:h-[325px] md:w-[420px] md:bg-gray-10;
}
.update-profile-picture-image-placeholder {
  @apply order-1 flex h-full flex-col items-center md:order-2 md:border-2 md:border-gray-10;
}
.update-profile-picture-placeholder-title {
  @apply flex w-[270px] py-meas7 text-center font-text-regular text-gray-50 xs:text-mobile-body-default md:w-[349px] md:py-meas12 md:text-tablet-body-default lg:text-desktop-body-default;
}
.update-profile-picture-placeholder-terms {
  @apply w-[270px] pt-meas8 font-text-regular text-gray-50 xs:text-mobile-caption1 md:w-[380px] md:pt-meas12 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.update-profile-picture-agreement-link {
  @apply text-navy-40 underline;
}
.update-profile-picture-preview {
  @apply h-[150px] max-h-[150px] w-[150px] max-w-[150px] md:h-[97px] md:max-h-[97px] md:w-[97px] md:max-w-[97px];
}
.update-profile-picture-label {
  @apply cursor-pointer;
}
