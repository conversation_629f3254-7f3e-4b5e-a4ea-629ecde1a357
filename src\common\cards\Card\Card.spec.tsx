import React from "react";
import { render, screen } from "@testing-library/react";
import Card, { CardProps } from "./Card";

describe("Card Component", () => {
  const defaultProps: CardProps = {
    children: <span>Test Content</span>
  };

  it("should render children correctly", () => {
    render(<Card {...defaultProps} />);

    expect(screen.getByText("Test Content")).toBeInTheDocument();
  });

  it("should have the correct class name", () => {
    const { container } = render(<Card {...defaultProps} />);

    expect(container.firstChild).toHaveClass("empty-card");
  });

  it("should update children correctly", () => {
    const { rerender } = render(<Card {...defaultProps} />);
    expect(screen.getByText("Test Content")).toBeInTheDocument();

    const newProps: CardProps = {
      children: <span>New Content</span>
    };
    rerender(<Card {...newProps} />);
    expect(screen.queryByText("Test Content")).not.toBeInTheDocument();
    expect(screen.getByText("New Content")).toBeInTheDocument();
  });

  it("should handle empty children", () => {
    const emptyProps: CardProps = {
      children: null
    };
    const { container } = render(<Card {...emptyProps} />);
    expect(container.firstChild).toBeEmptyDOMElement();
  });
});
