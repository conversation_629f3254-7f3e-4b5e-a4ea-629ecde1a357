.creator-code-form-container {
  @apply h-full w-full xs:mt-meas20;
}
.creator-code-form-title {
  @apply font-display-bold font-bold text-white xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}
.creator-code-form-description {
  @apply mt-meas8 font-text-regular text-[16px] font-normal leading-6 tracking-[0.8px] text-white md:mt-meas10;
}
.creator-code-form-tips {
  @apply mt-meas16;
}
.creator-code-form-tips > div {
  @apply mt-meas4 font-display-bold text-[20px] font-bold leading-7 tracking-[0px];
}
ul.creator-code-list-container li.creator-code-list {
  @apply flex;
}
ul.creator-code-list-container li.creator-code-list::before {
  @apply m-[10px] inline-block h-meas2 min-w-meas2 rounded-full bg-[#FFFFFF] content-[''];
}
.creator-code-form-validation-info-container {
  @apply mt-meas16 flex items-start;
}
.creator-code-form-validation-info-container > img {
  @apply mt-[2px];
}
.creator-code-form-validation-info-label {
  @apply ml-meas4 font-text-regular text-[16px] font-normal leading-6 text-white;
}
.creator-code-form-input-container {
  @apply mt-[1.875rem] md:mt-meas16 mb-meas16;
}
.creator-code-form-footer-container {
  @apply sticky mt-[1.875rem] w-full border-t-0 border-white border-opacity-[0.33] text-right md:mt-meas22;
}
.creator-code-form-footer-container svg.icon {
  @apply ml-meas4;
}
.creator-code-form-footer-container .btn {
  @apply bg-[#872AE6] px-meas8 py-meas6;
}
.creator-code-form-footer-container .btn:first-child {
  @apply mr-[10px] text-white;
  /* TODO Remove aftet integrating with theme (primary/tertiary buttons) */
  background-color: transparent;
}
.creator-code-form-footer-container .btn-primary:disabled {
  @apply border-error-60 bg-[#872AE6];
}
