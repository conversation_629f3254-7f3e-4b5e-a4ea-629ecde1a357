import React, { useEffect, useState } from "react";
import classNames from "classnames";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { Controller, useFormContext } from "react-hook-form";
import SecondaryFranchise from "../SecondaryFranchise/SecondaryFranchise";
import Search from "src/common/Search/Search";
import { FranchiseItem, FranchisesYouPlayFallbackImages, FranchisesYouPlayFormLabels } from "../franchises-you-play";

const PAGE_SIZE = 8;

const PrimaryFranchise = ({
  franchises,
  creator,
  franchisesYouPlayLabels,
  franchisesYouPlayFallbackImages,
  basePath
}: {
  franchises: FranchiseItem[];
  creator: { preferredSecondaryFranchises: FranchiseItem[]; preferredPrimaryFranchises: FranchiseItem };
  franchisesYouPlayLabels: FranchisesYouPlayFormLabels;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  basePath?: string;
}): JSX.Element => {
  const methods = useFormContext();
  const { control } = methods;
  const [franchiseOptions, setFranchiseOptions] = useState<FranchiseItem[]>([]);
  const [showLoadMore, setShowLoadMore] = useState(false);
  const [franchise, setPrimaryFranchise] = useState<FranchiseItem | null>(null);
  const updatePrimaryCard = (selectedItem: FranchiseItem) => {
    setPrimaryFranchise(selectedItem);
  };
  const secondaryFranchises = creator?.preferredSecondaryFranchises || [];
  const secondaryProps = {
    name: "secondaryFranchise",
    control: control,
    values: secondaryFranchises,
    disabled: franchise === null,
    basePath,
    items: franchiseOptions.map((item) => {
      return {
        ...item,
        checked:
          secondaryFranchises.filter((franchiseItem: FranchiseItem) => franchiseItem.value === item.value).length > 0
      };
    }),
    secondaryFranchisefallbackImage: franchisesYouPlayFallbackImages.secondaryFranchisefallbackImage
  };

  const addFranchiseOptions = () => {
    if (franchises.length > franchiseOptions.length) {
      if (franchises.length - franchiseOptions.length > PAGE_SIZE) {
        setFranchiseOptions(
          franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchiseOptions.length + PAGE_SIZE))
        );
      } else {
        setFranchiseOptions(franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchises.length)));
      }
    }
  };

  useEffect(() => {
    if (franchises.length > PAGE_SIZE) {
      setFranchiseOptions(franchises.slice(0, PAGE_SIZE));
    } else {
      setFranchiseOptions(franchises);
    }
  }, [franchises]);

  useEffect(() => {
    if (franchises.length > franchiseOptions.length) {
      setShowLoadMore(true);
    } else {
      setShowLoadMore(false);
    }
  }, [franchiseOptions]);

  useEffect(() => {
    if (creator?.preferredPrimaryFranchises) {
      franchises.filter((primaryFranchise) => {
        if (primaryFranchise.value === creator.preferredPrimaryFranchises.value) {
          setPrimaryFranchise(primaryFranchise);
        }
      });
    }
  }, [creator, franchises]);

  return (
    <>
      <div className="mg-primary-franchise-container">
        <div className="mg-primary-franchise">
          <h4 className="mg-primary-franchise-title">{franchisesYouPlayLabels.primaryFranchiseTitle}</h4>
          <div className="mg-primary-franchise-subtitle">{franchisesYouPlayLabels.primaryFranchiseSubTitle}</div>
        </div>
        {franchises && !!franchises.length && (
          <Controller
            control={control}
            name="primaryFranchise"
            rules={{ required: franchisesYouPlayLabels.messages.primaryFranchise }}
            defaultValue={creator.preferredPrimaryFranchises}
            render={({ field, fieldState: { error } }) => (
              <Search
                errorMessage={error?.message ?? ""}
                {...field}
                onChange={(item) => {
                  updatePrimaryCard(item as FranchiseItem);
                  field.onChange(item);
                }}
                options={franchises}
                placeholder={franchisesYouPlayLabels.labels.primaryFranchise}
              />
            )}
          />
        )}
        <div className="primary-franchise-option">
          <img
            alt="Selected Franchise"
            className={classNames(
              {
                "primary-franchise-selected": !!franchise
              },
              "primary-franchise-option-image"
            )}
            src={
              franchise?.image
                ? `${basePath}${franchise.image}`
                : franchisesYouPlayFallbackImages.primaryFranchisefallbackImage
            }
          />
        </div>
      </div>
      <div className="mg-sc-franchise-container">
        <div
          className={classNames({
            "mg-secondary-franchise-disabled": !franchise
          })}
        >
          <div className="mg-primary-franchise-title">{franchisesYouPlayLabels.secondaryFranchiseTitle}</div>
          <div className="mg-primary-franchise-subtitle">{franchisesYouPlayLabels.secondaryFranchiseSubTitle}</div>
        </div>
        {secondaryProps && <SecondaryFranchise {...secondaryProps} />}
        {franchise && showLoadMore && (
          <div className="secondary-franchise-load-more">
            <Button variant="secondary" size="md" onClick={addFranchiseOptions}>
              {franchisesYouPlayLabels.labels.loadMore}
            </Button>
          </div>
        )}
      </div>
    </>
  );
};
export default PrimaryFranchise;
