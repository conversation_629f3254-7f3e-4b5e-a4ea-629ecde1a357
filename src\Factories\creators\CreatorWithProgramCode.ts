import { Factory } from "fishery";
import { anAccountInformationWithPayableStatus } from "./AccountInformationWithPayableStatus";
import { aMailingAddress } from "./MailingAddress";
import { aPrimaryPlatform, aSecondaryPlatform } from "../platforms/PreferredPlatform";
import { aCommunicationPreferences } from "./CommunicationPreferences";
import { aConnectedAccounts } from "./ConnectedAccounts";
import { aPrimaryFranchise, aSecondaryFranchise } from "../franchises/PreferredFranchise";
import { aLegalEntity } from "./LegalEntity";
import { aAdditionalInformation } from "./AdditionalInformation";
import Random from "../Random";
import CreatorWithProgramCode, {
  CreatorWithProgramCodeResponse
} from "../../../src/shared/creators/CreatorWithProgramCode";

const factory = Factory.define<CreatorWithProgramCodeResponse>(() => ({
  id: Random.uuid(),
  avatar: Random.avatar(),
  creatorTypes: [Random.creatorType()],
  accountInformation: anAccountInformationWithPayableStatus(),
  mailingAddress: aMailingAddress(),
  communicationPreferences: aCommunicationPreferences(),
  connectedAccounts: aConnectedAccounts(),
  legalInformation: aLegalEntity(),
  preferredPlatforms: [aPrimaryPlatform(), aSecondaryPlatform()],
  preferredFranchises: [aPrimaryFranchise(), aSecondaryFranchise()],
  additionalInformation: aAdditionalInformation(),
  creatorConnectedProgram: Random.programCode(),
  socialLinks: [Random.url()]
}));

export function aCreatorWithProgramCode(override = {}): CreatorWithProgramCode {
  return CreatorWithProgramCode.fromApi(anApiCreatorWithProgramCode(override));
}

export function anApiCreatorWithProgramCode(override = {}): CreatorWithProgramCodeResponse {
  return factory.build(override);
}
