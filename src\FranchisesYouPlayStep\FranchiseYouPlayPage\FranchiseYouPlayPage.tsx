import React, { ReactElement, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  COMPLETED_ONBOARDING_STEPS,
  ERROR,
  LOADING,
  onToastClose,
  toastContent,
  useAsync,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "../../utils";
import {
  BrowserAnalytics,
  CloseHandler,
  Dispatch,
  ErrorHandling,
  FranchiseResponse,
  State,
  ValidationError
} from "../../types";
import PrimaryFranchise from "../PrimaryFranchise/PrimaryFranchise";
import Footer from "../../../src/common/Footer/Footer";
import MigrationModal, { LayoutType } from "../../../src/MigrationModal/MigrationModal";
import {
  FranchiseItem,
  FranchisesYouPlayFallbackImages,
  FranchisesYouPlayFormLabels,
  FranchisesYouPlayLabels,
  FranchisesYouPlayStepConfiguration
} from "../franchises-you-play";
import CancelRegistrationModal from "../../../src/common/CancelRegistrationModal/CancelRegistrationModal";
import Form from "../../utils/Form";
import { NextRouter } from "next/router";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import CreatorWithProgramCode from "src/shared/creators/CreatorWithProgramCode";
import { NavStep } from "src/CreatorTypeStep/CreatorTypePage/CreatorTypePage";
import { Toast } from "@eait-playerexp-cn/core-ui-kit";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import CreatorService from "@src/Browser/CreatorService";

type FranchisesFormProps = {
  franchises: FranchiseResponse[];
  creator: CreatorWithProgramCode | null;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  setShowMigration: (show: boolean) => void;
  showMigration: boolean;
  onClose: () => void;
  isPending: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  submitHandle: (data: any, navigateToPage: boolean, navigateBack: string) => void;
  stableDispatch?: Dispatch;
  router: NextRouter;
  navigateToPage: string;
  dispatch: Dispatch;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  state: State;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  basePath?: string;
};

const FranchisesForm = ({
  franchises,
  creator,
  franchisesYouPlayFormLabels,
  setShowMigration,
  showMigration,
  onClose,
  isPending,
  submitHandle,
  stableDispatch,
  router,
  navigateToPage,
  franchisesYouPlayFallbackImages,
  dispatch,
  breadcrumbLabels,
  state,
  basePath
}: FranchisesFormProps) => {
  const { getValues, formState } = useFormContext();
  const { userNavigated } = state;
  const data = getValues();
  const formModified = useMemo(() => !!Object.keys(formState.touchedFields).length || !!formState.isDirty, [formState]);

  const onSave = () => submitHandle(data, true, navigateToPage);

  /**
   * Discard will be called as follows:
   * - On click of Discard button from confirmation modal, which is shown when creator navigates away using back or previous stepper buttons
   */
  const onDiscard = () => {
    if (stableDispatch) {
      stableDispatch({ type: USER_NAVIGATED, data: false });
    }
    if (navigateToPage) {
      router.push(navigateToPage);
    } else {
      router.push("/creator-type");
    }
  };

  /**
   * User can navigate with back or using stepper links
   * - navigateToPage handles stepper links
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated && !formModified) {
      if (navigateToPage) {
        router.push(navigateToPage);
      } else {
        router.push("/creator-type");
      }
      if (dispatch) {
        dispatch({ type: USER_NAVIGATED, data: false });
      }
    }
  }, [formModified, router, userNavigated, navigateToPage]);

  const buttons = useMemo(
    () => ({
      cancel: franchisesYouPlayFormLabels?.buttons?.cancel ?? "",
      next: franchisesYouPlayFormLabels?.buttons?.next ?? ""
    }),
    [franchisesYouPlayFormLabels]
  );

  const layout = {
    buttons: franchisesYouPlayFormLabels.buttons
  } as unknown as LayoutType;

  return (
    <>
      <div className="franchises-you-play-form">
        <div className="franchises-you-play">
          <PrimaryFranchise
            {...{
              franchises: franchises as unknown as FranchiseItem[],
              creator: creator as unknown as {
                preferredSecondaryFranchises: FranchiseItem[];
                preferredPrimaryFranchises: FranchiseItem;
              },
              basePath,
              franchisesYouPlayLabels: franchisesYouPlayFormLabels,
              franchisesYouPlayFallbackImages
            }}
          />
        </div>
        <Footer {...{ buttons, onCancel: onClose, disableSubmit: isPending, isPending }} />
      </div>
      <MigrationModal {...{ setShowMigration, showMigration, onSave, onDiscard, layout, breadcrumbLabels }} />
    </>
  );
};

type FranchisesYouPlayPageProps = {
  stableDispatch: Dispatch;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  franchisesYouPlayLabels: FranchisesYouPlayLabels;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  showMigration: boolean;
  setShowMigration: (value: boolean) => void;
  PROGRAM_CODE: string;
  navigateToPage: string;
  dispatch: Dispatch;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  breadcrumbLabels: {
    modalTitle: string;
    modalMessage: string;
  };
  router: NextRouter;
  locale: string;
  errorHandling: ErrorHandling;
  analytics: BrowserAnalytics;
  configuration: FranchisesYouPlayStepConfiguration;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  unhandledError: string;
  state: State;
  onClose: () => void;
  basePath?: string;
  onContinueToNextPage?: () => void;
};
export default function FranchisesYouPlayPage({
  showConfirmation,
  setShowConfirmation,
  analytics,
  navigateToPage,
  configuration,
  state,
  showMigration,
  setShowMigration,
  stableDispatch,
  errorHandling,
  router,
  breadcrumbLabels,
  franchisesYouPlayLabels,
  franchisesYouPlayFormLabels,
  dispatch,
  errorToast,
  onClose,
  franchisesYouPlayFallbackImages,
  PROGRAM_CODE,
  unhandledError,
  basePath,
  onContinueToNextPage
}: FranchisesYouPlayPageProps): JSX.Element {
  const { isError = false, isValidationError = false, onboardingSteps = [] } = state;
  const [creator, setCreator] = useState<CreatorWithProgramCode | null>(null);
  const [franchises, setFranchises] = useState<FranchiseResponse[]>([]);
  const { title, description, confirmationDesc1, confirmationDesc2, modalConfirmationTitle } = franchisesYouPlayLabels;
  const handleModalClose = useCallback(() => setShowConfirmation(false), []);

  const creatorService = configuration.creatorsClient
    ? new CreatorsService(configuration.creatorsClient, configuration.DEFAULT_AVATAR_IMAGE)
    : undefined;
  const legacyCreatorService = !configuration.creatorsClient
    ? new CreatorService(configuration.onBoardingClient)
    : undefined;
  const metadataService = new MetadataService(configuration.metadataClient);

  const handleCancelRegistration = useCallback(() => {
    if (analytics.canceledOnboardingFlow) {
      analytics.canceledOnboardingFlow({ locale: router.locale as string, page: router.pathname });
    }
    router.push("/api/logout");
  }, [router]);

  /** Form submission is required in following scenarios
   * 1. On click of Next button
   * 2. On click of save button in confirmation modal, when creator tries to navigate away with back or stepper buttons
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const submitHandle: any = useCallback(
    async (
      data: { primaryFranchise: FranchiseItem; secondaryFranchise: FranchiseItem[] },
      navigateBack: string,
      navigateToPage: string
    ) => {
      try {
        stableDispatch({ type: USER_NAVIGATED, data: false });
        const currentStep = (onboardingSteps as Array<NavStep>).find((step) => step.href === router.pathname);
        const accountInformation = {
          ...(creator as CreatorWithProgramCode).accountInformation,
          dateOfBirth: LocalizedDate.format(
            ((creator as CreatorWithProgramCode).accountInformation.dateOfBirth as unknown as LocalizedDate).toDate(),
            "YYYY-MM-DD"
          )
        };
        const primaryFranchiseValue = { id: data.primaryFranchise.value, type: "PRIMARY" };
        const values = data.secondaryFranchise.map(
          (secondaryFranchise) =>
            secondaryFranchise.value && {
              id: secondaryFranchise.value,
              type: "SECONDARY"
            }
        );
        values.push(primaryFranchiseValue);

        const payload = {
          accountInformation,
          franchisesYouPlay: data,
          creatorConnectedProgram: PROGRAM_CODE
        } as unknown as Partial<CreatorWithProgramCode>;

        if (legacyCreatorService) {
          await legacyCreatorService.update(
            payload as unknown as UpdateCreatorRequest & Partial<CreatorWithProgramCode>
          );
        } else {
          await (creatorService as CreatorsService).updateCreator({
            preferredFranchises: values,
            program: { code: PROGRAM_CODE }
          } as UpdateCreatorRequest);
        }
        stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
        if (!legacyCreatorService) {
          (creator as unknown as { preferredPrimaryFranchise: Record<string, string> }).preferredPrimaryFranchise =
            data.primaryFranchise;
        } else {
          (creator as unknown as { preferredPrimaryFranchises: Record<string, string> }).preferredPrimaryFranchises =
            data.primaryFranchise;
        }
        (creator as unknown as { preferredSecondaryFranchises: FranchiseItem[] }).preferredSecondaryFranchises =
          data.secondaryFranchise;

        if (analytics.confirmedFranchise) {
          analytics.confirmedFranchise({
            locale: router.locale as string,
            creator: creator as unknown as Record<string, unknown>
          });
        }

        // Navigate to next page using callback or fallback to default navigation
        if (onContinueToNextPage) {
          onContinueToNextPage();
        } else {
          // Fallback navigation logic for backward compatibility
          if (navigateToPage) {
            router.push(navigateToPage);
          } else if (navigateBack) {
            router.push("/creator-type");
          } else {
            router.push("/connect-accounts");
          }
        }
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
      }
    },
    [stableDispatch, creator, router, onboardingSteps]
  );

  const { pending: isPending, execute: submitHandleClb } = useAsync(submitHandle, false);

  useEffect(() => {
    async function fetchData() {
      try {
        stableDispatch({ type: LOADING, data: true });
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const creator: any = legacyCreatorService
          ? (await legacyCreatorService.getCreatorWithProgramCode()).data
          : await creatorService?.getCreator(PROGRAM_CODE);
        if (!legacyCreatorService) {
          creator.preferredPrimaryFranchises = {
            value: creator?.preferredPrimaryFranchise?.id || "",
            label: creator?.preferredPrimaryFranchise?.name || ""
          };
          creator.preferredSecondaryFranchises = creator.preferredSecondaryFranchises.map(
            (platform: { id: string; name: string }) => {
              return {
                ...platform,
                value: platform.id,
                label: platform.name
              };
            }
          );
        }
        setCreator(creator as unknown as CreatorWithProgramCode);

        const allFranchises = await metadataService.getFranchises();
        const supportedFranchises = configuration.supportedFranchises
          ? allFranchises.filter((franchise) => configuration.supportedFranchises?.includes(franchise.value))
          : allFranchises;
        if (supportedFranchises) {
          setFranchises(supportedFranchises as unknown as FranchiseResponse[]);
        }
        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        errorHandling(stableDispatch, e as Error);
        stableDispatch({ type: LOADING, data: false });
      }
    }
    fetchData();
  }, [stableDispatch]);

  const cancelRegistrationModalLabels = {
    title: modalConfirmationTitle,
    yes: franchisesYouPlayLabels.buttons.yes,
    no: franchisesYouPlayLabels.buttons.no,
    close: franchisesYouPlayLabels.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={(isError || toastContent(isValidationError as unknown as ValidationError[])) as ReactNode}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  return (
    <div className="mg-page-onboarding-creator">
      <div className="onboarding-creator-franchises-container">
        <div className="mg-franchises-you-play">
          <h3 className="mg-franchises-you-play-title">{title}</h3>
          <div className="mg-franchises-you-play-description">{description}</div>
        </div>
        {franchises && creator && (
          <Form mode="onChange" onSubmit={submitHandleClb}>
            <FranchisesForm
              {...{
                franchises,
                basePath,
                breadcrumbLabels,
                franchisesYouPlayFormLabels,
                franchisesYouPlayFallbackImages,
                dispatch,
                creator,
                franchisesYouPlayLabels,
                setShowMigration,
                state,
                showMigration,
                onClose,
                isPending,
                submitHandle,
                stableDispatch,
                router,
                navigateToPage
              }}
            />
          </Form>
        )}
        {showConfirmation && (
          <CancelRegistrationModal
            {...{
              labels: cancelRegistrationModalLabels,
              handleModalClose,
              handleCancelRegistration
            }}
          />
        )}
      </div>
    </div>
  );
}
