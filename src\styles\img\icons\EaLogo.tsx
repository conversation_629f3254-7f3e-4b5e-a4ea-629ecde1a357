import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import * as React from "react";

const SvgEaLogo: React.FC<SvgProps> = (props) => {
  const { width = "1rem", height = "1rem" } = props;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="ea-logo"
    >
      <rect width="40" height="40" rx="20" fill="#385CFF" />
      <path d="M23.9709 13.6094H12.0675L10.2493 16.5567H22.2095L23.9709 13.6094Z" fill="white" />
      <path
        d="M19.9936 19.1924H8.34588L6.47088 22.0547H9.08452L5.47656 27.6094H20.6754L26.3572 19.1924L28.3743 22.0547H26.6697L24.9084 24.747H30.2209L32.0391 27.6094H35.4766L26.3572 13.6094L19.0561 24.747H10.8459L12.6072 22.0547H18.2038L19.9936 19.1924Z"
        fill="white"
      />
    </svg>
  );
};

export default SvgEaLogo;
