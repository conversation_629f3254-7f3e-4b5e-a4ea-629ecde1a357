import React, { ComponentType, ReactElement } from "react";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import TermsAndConditions, { InformationLabels } from "./terms-and-conditions";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { PageLabels } from "src/InformationStep/OnboardingInformationStep";

const meta: Meta<typeof TermsAndConditions> = {
  title: "Component Library/Terms And Conditions Component",
  component: TermsAndConditions,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof TermsAndConditions>;

const dispatch = (): void => {};

export const TermsAndConditionsComponent: Story = {
  args: {
    termsAndConditionsLabels: {
      title: "Terms & Conditions",
      titleUpdatedTermsAndConditions: "Our terms and conditions have changed.",
      subTitleNewUser:
        "Welcome to the EA Creator Network. In order to become a creator, you must read and agree to the EA Creator Network agreement. If you do not agree to these terms, do not sign them and please exit the browser, in which case you will not be able to participate in the EA Creator Network.",
      subTitleExistingUser:
        "Welcome back to the EA Creator Network. We've revised our Electronic Arts Creator Influencer Agreement. In order to continue to participate as an Electronic Arts Creator, you must read and agree to the updated Electronic Arts Creator Influencer Agreement. If you do not agree to these updated terms, do not sign them and please exit the browser, in which case you will not be able to continue to participate as an Electronic Arts Creator.",
      subTitleUpdatedTermsAndConditions:
        "We've updated our disclosure requirements. Reach out to your Community Manager if you have any questions. Please review and accept our updated terms and conditions to continue.",
      businessName: "Business Name",
      businessNameRequired: "Business Name is Required",
      alreadySigned: "You have already signed Terms & Conditions. Please click Next to proceed",
      street: "Street",
      city: "City",
      country: "Country/Region",
      state: "State or Province",
      zip: "Zip Code or Postal Code",
      sameAddress: "Address is the same as my mailing address.",
      entityType: "Entity Type",
      entityIndividual: "Individual",
      entityBusiness: "Business",
      enterDetails: "To review the Creator Network Terms and Conditions, please enter your legal name and information.",
      firstName: "First Name",
      lastName: "Last Name",
      screenName: "Screen Name",
      email: "Email",
      businessNameInputDesc: "Only if you are contracting under a business entity; otherwise, leave blank.",
      decline: "Decline",
      declineModalHeader: "Are you sure you want to decline?",
      declineModalDescription:
        "You must accept the T&Cs in order to continue using Creator Network. If you decline, you will be logged out.",
      contractTitle: "Electronic Arts Creator Influencer Agreement",
      contractDescription: "Please review and accept the Electronic Arts Creator Influencer Agreement to continue."
    },
    layout: {
      buttons: {
        yes: "Yes",
        no: "No",
        cancel: "Cancel",
        next: "Next",
        close: "Close",
        declineTermsAndCondition: "Decline",
        save: "Save",
        discard: "Discard"
      }
    },
    breadcrumbLabels: {
      modalTitle: "modalTitle",
      modalMessage: "modalMessage"
    },
    pageLabels: {
      confirmationDesc1: "confirmationDesc1",
      confirmationDesc2: "conf'irmationDesc2",
      modalConfirmationTitle: "modalConfirmationTitle",
      unhandledError: "unhandledError",
      creatorTitle: "",
      yes: "",
      no: "",
      userDescription1: "",
      userDescription2: "",
      infoLabels: {
        messages: {
          firstNameTooLong: "firstNameTooLong",
          lastNameTooLong: "lastNameTooLong",
          street: "street",
          streetTooLong: "streetTooLong",
          city: "city",
          cityTooLong: "cityTooLong",
          state: "state",
          stateTooLong: "stateTooLong",
          zipCode: "zipCode",
          zipCodeTooLong: "zipCodeTooLong",
          tShirtSize: "",
          entityType: "",
          businessName: "businessName",
          businessNameTooLong: "businessNameTooLong",
          email: "email",
          emailTooLong: "emailTooLong",
          emailInvalid: "emailInvalid",
          url: "url",
          invalidUrl: "invalidUrl",
          followersMaxLength: "followersMaxLength",
          firstName: "firstName",
          lastName: "lastName",
          dateOfBirth: "dateOfBirth",
          dateOfBirthInvalid: "dateOfBirthInvalid",
          ageMustBe18OrOlder: "ageMustBe18OrOlder",
          country: "country"
        }
      }
    } as PageLabels & { infoLabels: InformationLabels } & { unhandledError: string },
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: dispatch,
    state: {
      userNavigated: false
    },
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      onBoardingClient: {
        get: () => Promise.resolve({}),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      creatorsClient: undefined,
      metadataClient: {} as unknown as TraceableHttpClient,
      navigateToNextPage: "/creator-code",
      enableCommunicationPreferencesTab: true,
      navigateToPreviousPage: "/communication-preferences",
      DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png"
    }
  }
};
